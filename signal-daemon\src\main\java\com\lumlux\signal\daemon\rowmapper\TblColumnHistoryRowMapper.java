package com.lumlux.signal.daemon.rowmapper;


import com.lumlux.signal.daemon.bean.TblColumnHistory;
import com.lumlux.signal.daemon.util.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public final class TblColumnHistoryRowMapper implements RowMapper<TblColumnHistory> {

    @Override
    public TblColumnHistory mapRow(ResultSet rs, int index)
            throws SQLException {
        TblColumnHistory to = new TblColumnHistory();
        to.setId(rs.getString("id"));
        to.setColumnId(rs.getString("column_id"));
        to.setRedProjectorPower(rs.getString("red_projector_power"));
        to.setGreenProjectorPower(rs.getString("green_projector_power"));
        to.setDelFlag(rs.getString("del_flag"));
        return to;
    }
}
