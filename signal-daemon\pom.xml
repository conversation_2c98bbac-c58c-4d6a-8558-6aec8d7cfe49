<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>signal</groupId>
		<artifactId>signal</artifactId>
		<version>1.0</version>
	</parent>
	<artifactId>signal-daemon</artifactId>
	<packaging>jar</packaging>
	<properties>
		<flashSeconds>20</flashSeconds>
		<intervalTime>40000</intervalTime>
		<accessId>LTAIj95PPCudoHcS</accessId>
		<accessSecret>tScgSgZWu9qV8Mr0Zk0h3l9E6u8Xm3</accessSecret>
		<endPoint>cn-hangzhou</endPoint>
		<accessKeyId>S5lYinngPypZKgGD</accessKeyId>
		<accessKeySecret>EeEpqT8RAjuJ6ApERV8YbeETQ4hNI6</accessKeySecret>
		<projectName>yp_znjtxhdxt</projectName>
		<topicName>tbl_message</topicName>
		<informUser>guguoyong</informUser>
		<officeId>38dec8499c0449ffa7e7704eb159f3ac</officeId>
		<onlineMinutes>3</onlineMinutes>
		<saveHistory>0</saveHistory>
		<saveHistoryCollector>18918050519|ALL</saveHistoryCollector>
		<redis.host>127.0.0.1</redis.host>
		<redis.port>6380</redis.port>
		<redis.pass>123456</redis.pass>
		<redis.maxIdle>300</redis.maxIdle>
		<redis.maxActive>600</redis.maxActive>
		<redis.maxWait>100000</redis.maxWait>
		<redis.testOnBorrow>true</redis.testOnBorrow>
		<!-- 1企业微信   2政务微信 -->
		<!-- 企业微信 -->
		<WxType>1</WxType>
		<reqUrl>https://qyapi.weixin.qq.com</reqUrl>
		<corpid>ww429d7e929bd28e18</corpid>
		<corpAppSecret>w13R-4EEK0-8cMLhYy7r5UZp0ceBbpEsHuM6uNHDwaE</corpAppSecret>
		<agentId>1000002</agentId>
		<dataHubSave>0</dataHubSave>
		<endPointAddress>http://************</endPointAddress>
		<!-- 政务微信  -->
		<!-- <WxType>2</WxType>
		<reqUrl>https://jwwx.police.sh.cn</reqUrl>
		<corpid>wlf6450fdb4c</corpid>
		<corpAppSecret>SAvvucQVe6pbVSjFaW3nPw4HsV_7bnaZlTgnjQpd4l0</corpAppSecret>
		<agentId>1000174</agentId>
		<dataHubSave>0</dataHubSave>-->
		<!-- 以下是数据库-->
		<!-- 本地 -->
		<!--<database.jdbc.url><![CDATA[******************************************************************************************************************************]]></database.jdbc.url>-->
				<database.jdbc.url><![CDATA[****************************************************************************************************************************************]]></database.jdbc.url>
				<database.jdbc.name>signal_db</database.jdbc.name>
				<database.jdbc.password>signal_123_A</database.jdbc.password>
<!--		<database.jdbc.url><![CDATA[*********************************************************************************************************]]></database.jdbc.url>-->
<!--		<database.jdbc.name>root</database.jdbc.name>-->
<!--		<database.jdbc.password>root123456</database.jdbc.password>-->
		<appid>wx2c36b1a29053d750</appid>
		<secret>0f7362bd339df96f8e36f576d217f7f5</secret>
		<publicAppid>wx8ecfd27d184603da</publicAppid>
		<publicSecret>254bc36f664057f81efd4ed498f4cea6</publicSecret>
		<templateId>cGRCLpsgz9XQBsqYLEibjTMOO7YfuLOnqv6h2Dwfkyc</templateId>
		<replyTemplateId>STdlIFhg9dwJ7FqheBQuvsYta8ayuL_h2SVOTaVan18</replyTemplateId>
		<!-- 本地 -->
		<!-- 会为 -->
		<!-- <database.jdbc.url><![CDATA[****************************************************************************************************************]]></database.jdbc.url>
		<database.jdbc.name>signal_db</database.jdbc.name>
		<database.jdbc.password>signal_123_A</database.jdbc.password>
		<appid>wx67d8ccfe9d1dffaf</appid>
		<secret>48947e66b979ae67798889dd55999851</secret>
		<publicAppid>wx27a0d3ceae3deeb9</publicAppid>
		<publicSecret>9e14c1e7b067277e1cb0fd82ea9eefb1</publicSecret>
		<templateId>uRFvVCVz8sBK5bkykOnUG4V6gUsVlKwzaQQK4anK8ko</templateId>
		<replyTemplateId>jGUUaTPpGNovDajGoC2v1W5xkv-aksfeg4eohhl15SM</replyTemplateId> -->
		<!-- 会为 -->
	</properties>
	<dependencies>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.14</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>4.0.2.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.0.11</version>
		</dependency>
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-filter-compression</artifactId>
			<version>2.0.4</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.3.0</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.4</version>
		</dependency>
		
		<dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
		    <groupId>commons-pool</groupId>
		    <artifactId>commons-pool</artifactId>
		    <version>1.6</version>
		</dependency>
        <!-- 阿里语音短信服务 -->
		<dependency>
		    <groupId>com.aliyun</groupId>
		    <artifactId>aliyun-java-sdk-core</artifactId>
		    <version>4.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dyvmsapi</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>net.sf.json-lib</groupId>
		    <artifactId>json-lib</artifactId>
		    <version>2.4</version>
		    <classifier>jdk15</classifier>
		</dependency>
		<!-- datahub -->
		<dependency>
		    <groupId>com.aliyun.datahub</groupId>
		    <artifactId>aliyun-sdk-datahub</artifactId>
		    <version>2.9.2-public</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>4.0.2.RELEASE</version>
<!--			<scope>compile</scope>-->
		</dependency>
		<dependency>
			<groupId>com.heinqi.yangtes</groupId>
			<artifactId>yangtes-jee-commons</artifactId>
			<version>1.0.71</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.13.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.15</version>
		</dependency>

		<!-- poi 读取word doc-->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.15</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>3.15</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							**/*.properties
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.2-beta-5</version>
				<executions>
					<execution>
						<id>make</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<skipAssembly>false</skipAssembly>
							<finalName>daemon</finalName>
							<descriptors>
								<descriptor>src/main/assembly/daemon.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>3.5.0</version>
				<extensions>true</extensions>
			</plugin>
		</plugins>
	</build>
</project>