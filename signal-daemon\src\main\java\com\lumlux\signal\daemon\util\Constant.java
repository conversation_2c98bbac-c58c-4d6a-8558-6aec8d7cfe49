package com.lumlux.signal.daemon.util;

import java.util.Arrays;
import java.util.List;

/**
 * <p>Title: Constant.java</p>
 * <p>Description: Constant单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/4/29</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class Constant {

    static String []  array = {"01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31",
            "32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59","60","61","62","63","64",
            "65","66","67","68","69","70","71","72","73","74","75","76","77","78","79","80","81","82","83","84","85",
            "86","87","88","89","90","91","92","93","94","95","96","97","98","99","100"};

/*    interface SCHEME_NUNBER_STATUS{
        String FIRST ="1";
        String  TWO = "2";
        String  THREE = "3";

        int  HUANG_SHAN_SCHEME = 0; // 黄闪
        int  TOTAL_TURNOFF_SCHEME = 255; //全灭

    }*/


    public static void main(String[] args) {


        List<String> arrayStatusList = Arrays.asList(array);

        if (arrayStatusList.contains("99")){

            System.out.println("我来了。。。");
        }
    }
}
