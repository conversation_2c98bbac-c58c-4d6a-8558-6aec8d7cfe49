<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" %>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>

<layout:default title="配置时段" bodyId="collectorId">
	<style>
    	.input-width-20{
	    	width:20px;
	    	text-align: center;
	    }
	    .float-left{
	    	padding-left:2px;
	    	float:left;
	    }
	    .control-group{
	    	border:0;
	    }
	    .alert{
			margin-bottom: 20px;
		}

		input{
			width: 40px;
		}

    </style>
	<script type="text/javascript">
		$(document).ready(function() {
			$("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});
			$(".input-width-20").blur(function(){
	    		if(this.value.length==0){
	    			this.value="00";
	    		}else if(this.value.length==1){
	    			this.value= "0" + this.value;
	    		}else if(this.value.length==2){
	    			if($(this).hasClass("type-hour")){
	    				if(parseInt(this.value)<0){
	    					this.value="00";
	    				}else if(parseInt(this.value)>23){
	    					this.value="23";
	    				}
	    			}else if($(this).hasClass("type-minute")){
	    				if(parseInt(this.value)<0){
	    					this.value="00";
	    				}else if(parseInt(this.value)>59){
	    					this.value="59";
	    				}
	    			}
	    		}
	    	});
        });


		// 显示屏下放
		var set = function(){
			top.$.jBox.tip("正在下放，请稍等...",'loading',{opacity:0});
			let code = "${form.condition.code}";
			const collector ={
				"code":code
			}
			params = {...collector}
			$.ajax({
				type: "post",
				url: "${path}/column/a/signal/tblColumn/secondPutAlarmConfig",
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				data: JSON.stringify(params),
				success: result => {
					if(result.code == 200) {
						top.$.jBox.tip("下放成功", 'success', {opacity: 0});
					} else {
						top.$.jBox.tip("下放失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
					}
				},
				error: _ => top.$.jBox.tip("下放失败", 'error', {opacity: 0})
			})
		}

		function onShaftThresholdBtnClick() {
			let collectorId = "${form.condition.collectorId}";
			$.ajax({
				type: "POST",
				url:  "${ctx}/signal/tblColumn/getShiftThreshold?collectorId=" + collectorId,
				success: res => {
					$("#xShaftThresholdInput").val(res.data.thresholdValuex)
					$("#yShaftThresholdInput").val(res.data.thresholdValuey)
					$("#zShaftThresholdInput").val(res.data.thresholdValuez)
				},

			});
		}
		function setShaftThreshold(){
			let collectorId = "${form.condition.collectorId}";
			let thresholdX =  Number($("#xShaftThresholdInput").val())
			let thresholdY =  Number($("#yShaftThresholdInput").val())
			let thresholdZ =  Number($("#zShaftThresholdInput").val())
			params = {id: collectorId,xShaft: thresholdX,yShaft: thresholdY,zShaft: thresholdZ}

			if(isNaN(thresholdX) || thresholdX <= 0 || isNaN(thresholdY) || thresholdY <= 0 || isNaN(thresholdZ) || thresholdZ <= 0)
				top.$.jBox.tip("请输入正数作为阈值", 'error', {opacity: 0})
			else {
				$.ajax({
					type: "post",
					url: "${path}/column/a/signal/tblColumn/updateAttitude",
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					data: JSON.stringify(params),
					success: result => {
						if(result.code == 200) {
							top.$.jBox.tip("设置成功", 'success', {opacity: 0});
						} else {
							top.$.jBox.tip("设置失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
						}
					},
					error: _ => top.$.jBox.tip("设置失败", 'error', {opacity: 0})
				})

			}
		}

		var onShaftBtnClick = function () {
			top.$.jBox.tip("正在复位，请稍等...",'loading',{opacity:0});
			let collectorId = "${form.condition.collectorId}";
			params = {id: collectorId}
			$.ajax({
				type: "post",
				url: "${path}/column/a/signal/tblColumn/resetShaft",
				contentType: "application/json; charset=utf-8",
				dataType: "json",
				data: JSON.stringify(params),
				success: result => {
					if(result.code == 200) {
						top.$.jBox.tip("复位成功", 'success', {opacity: 0});
					} else {
						top.$.jBox.tip("设置失败", 'error', {opacity: 0})
					}
				},
				error: _ => top.$.jBox.tip("复位失败", 'error', {opacity: 0})
			})
		}


		var onSaveBtnClick = function() {
			const paramKey = ["collectorName","stateInterval","statusOfflineInterval","statusPartInvalidInterval","id"]
			const formItemName = [
				"condition.collectorName", "dataEntity.stateInterval" ,"dataEntity.statusOfflineInterval","dataEntity.statusPartInvalidInterval","condition.collectorId"]

		const rawParams = $("#inputForm").serializeArray()
		let params = {}

		for (let i = 0; i < paramKey.length; i++)
			params[paramKey[i]] = rawParams.filter(e=> e.name == formItemName[i])[0]['value']
		params = {...params}

		$.ajax({
			type: "post",
			url: "${path}/column/a/signal/tblColumn/updateColumnConfig",
			contentType: "application/json",
			dataType: "json",
			data: JSON.stringify(params),
			success: result => {
				if(result.code == 200)
					top.$.jBox.tip("保存成功", 'success', {opacity: 0});
				else
					top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
			},
			error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0})
		})
		}

	</script>

	<ul class="nav nav-tabs">
		<li><a href="${ctx}/signal/tblCollector/listColumn?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">立柱列表</a></li>
		<li class="active"><a href="${ctx}/signal/tblAlarmConfig/pillarSecondParamDetail?condition.collectorId=${form.dataEntity.collectorId}">详细配置</a></li>
	</ul><br/>
<%--	<form:form id="inputForm" modelAttribute="form" action="${ctx}/signal/tblCollector/save?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" method="post" class="form-horizontal">--%>
	<form:form id="inputForm" modelAttribute="form" action="${ctx}/signal/tblColumn/updateColumnConfig" method="post" class="form-horizontal">
		<form:hidden path="dataEntity.id"/>
		<form:hidden path="condition.collectorId"/>
		<form:hidden path="condition.code"/>
		<input id="dataEntityId" name="dataEntity.id" type="hidden" value="${form.condition.collectorId}"/>
		<sys:message content="${message}"/>
		<sys:message content="${ifExistMsg}" type="error"/>
	<%--	<div style="margin-bottom:20px;font-size: 14px;">集中器名称：${form.condition.collectorName}</div>--%>
		<div class="control-group input-group" style="margin-top: 10px;">
			<label class="control-label">集中器名称：</label>
			<div class="controls">
				<form:input path="condition.collectorName" readonly="true" htmlEscape="false" style="width: 270px;" class="input-xlarge"/>
			</div>
		</div>
		<div class="control-group input-group">
			<label class="control-label">心跳包间隔：</label>
			<div class="controls">
				<form:input path="dataEntity.stateInterval" htmlEscape="false" maxlength="5" class="input-xlarge"/>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">数据包传输间隔：</label>
			<div class="controls">
				<form:input path="dataEntity.statusOfflineInterval" htmlEscape="false" maxlength="20" class="input-xlarge required number type-text"/>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">系统重启：</label>
			<div class="controls">
				<form:input path="dataEntity.statusPartInvalidInterval" htmlEscape="false" maxlength="20" class="input-xlarge required number type-text"/>
				<label style="color: red">* 0-正常 1-重启 其他值-无效</label>
			</div>
		</div>


		<!-- 按钮 -->
		<div id="btns" class="form-actions">

			<shiro:hasPermission name="signal:tblColumn:edit">
				<input id="btnSubmit"   class="btn btn-primary" value="保 存" onclick="onSaveBtnClick()"/>&nbsp;
			</shiro:hasPermission>
			<shiro:hasPermission name="signal:tblColumn:put">
				<a id="setBtn" onclick="set()"  class="btn btn-primary disableBtn">下 放</a>
			</shiro:hasPermission>

			<shiro:hasPermission name="signal:tblColumn:edit">
				<input id="btnSubmit"   class="btn btn-primary" value="位姿复位" style="width: 60px;margin-left: 20px" onclick="onShaftBtnClick()"/>&nbsp;
			</shiro:hasPermission>
			<shiro:hasPermission name="signal:tblColumn:edit">
				<a class="btn btn-primary" data-toggle="modal" data-target="#shaftThresholdModal"  style="width: auto" onclick="onShaftThresholdBtnClick()">位姿偏移阈值</a>
				<%--				<input id="btnSubmit"   class="btn btn-primary" value="位姿偏移阈值设置"  style="width: auto" onclick="onShaftValueSaveBtnClick()"/>&nbsp;--%>
			</shiro:hasPermission>

			<a id="btnCancel" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>
		</div>

		<%--<div>
			<shiro:hasPermission name="signal:tblColumn:edit">
				<input id="btnSubmit"   class="btn btn-primary" value="位姿复位" style="width: 60px" onclick="onShaftBtnClick()"/>&nbsp;
			</shiro:hasPermission>
			<shiro:hasPermission name="signal:tblColumn:edit">
				<a class="btn btn-primary" data-toggle="modal" data-target="#shaftThresholdModal"  style="width: auto" onclick="onShaftThresholdBtnClick()">位姿偏移阈值</a>
&lt;%&ndash;				<input id="btnSubmit"   class="btn btn-primary" value="位姿偏移阈值设置"  style="width: auto" onclick="onShaftValueSaveBtnClick()"/>&nbsp;&ndash;%&gt;
			</shiro:hasPermission>
		</div>--%>
		<div class="modal fade" id="shaftThresholdModal" tabindex="-1" role="dialog" aria-labelledby="shaftThresholdModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
						<h4 class="modal-title" id="shaftThresholdModalLabel">配置位姿偏移阈值</h4>
					</div>
					<div class="modal-body">
						<div style="text-align: center">
							<div style="margin-bottom: 5px">
								<label for="xShaftThresholdInput">x轴位姿偏移阈值：</label>
								<input id="xShaftThresholdInput" value="1"/>
							</div>
							<div style="margin-bottom: 5px">
								<label for="yShaftThresholdInput">y轴位姿偏移阈值：</label>
								<input id="yShaftThresholdInput" value="2"/>
							</div>
							<div style="margin-bottom: 5px">
								<label for="zShaftThresholdInput">z轴位姿偏移阈值：</label>
								<input id="zShaftThresholdInput" value="3"/>
							</div>
							<a class="btn btn-primary" style="width: 28px;height: 20px;display: inline-block;"
							   onclick="setShaftThreshold()">确定
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>

	</form:form>
</layout:default>