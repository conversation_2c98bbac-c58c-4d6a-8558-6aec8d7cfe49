<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>
<%@ taglib prefix="ui" tagdir="/WEB-INF/tags/ui" %>


<layout:default title="集中器表管理">
	<style>
		.longContent {
			width: 10%;
			/*max-width: 235px;*/
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}

		.controls >a {
			-webkit-user-drag: none;
		}
		.controls >a >input{
			border:1px solid !important;
			margin: 0 2px 5px 5px !important;
		}
		.controls >a >input:last-child {
			border:1px solid !important;
			margin: 0 5px 5px 2px !important;
		}
	</style>
	<script type="text/javascript">
		$(document).ready(function() {
			
		});
		function page(n,s){
			$("#pageNo").val(n);
			$("#pageSize").val(s);
			$("#searchForm").submit();
        	return false;
        }
        function editColumnItem(id){
        	$("#dataEntityId").val(id);
        	$("#searchForm").attr("action","${ctx}/signal/tblCollector/signalBoardForm");
        	<%--$("#searchForm").attr("action","${ctx}/signal/tblCollector/form");--%>
			$("#searchForm").submit();
        	return false;
        }

        function deleteItem(id,enabled,redProjectorEnabled,greenProjectorEnabled,hornEnabled,displayEnabled,attitudeEnabled){

        	confirmx('确认要删除该控制器吗？', function(){
        		$("#dataEntityId").val(id);
                $("#enabled").val(enabled);

				$("#redProjectorEnabled").val(redProjectorEnabled);
				$("#greenProjectorEnabled").val(greenProjectorEnabled);
				$("#hornEnabled").val(hornEnabled);
				$("#displayEnabled").val(displayEnabled);
				$("#attitudeEnabled").val(attitudeEnabled);
	        	$("#searchForm").attr("action","${ctx}/signal/tblCollector/deleteColumn");
				$("#searchForm").submit();
				return true;
        	});
        	return false;
        }

        function enableColumn(id,enabled){
            var msg = enabled=='0'?'不':'';
            confirmx('确认要'+msg+'启用该标志牌吗？', function(){
				$("#dataEntityId").val(id);
				$("#model").val();

                $("#enabled").val(enabled);
                $("#searchForm").attr("action","${ctx}/signal/tblCollector/enableColumn");
                $("#searchForm").submit();
            });

            return false;
        }

		function enableRedProjector(id,redProjectorEnabled){
			var msg = redProjectorEnabled=='0'?'不':'';
			confirmx('确认要'+msg+'启用通道2吗？', function(){
				$("#dataEntityId").val(id);
				$("#redProjectorEnabled").val(redProjectorEnabled);
				$("#searchForm").attr("action","${ctx}/signal/tblCollector/enableRedProjector");
				$("#searchForm").submit();
			});

			return false;
		}
		function enableGreenProjector(id,greenProjectorEnabled){
			var msg = greenProjectorEnabled=='0'?'不':'';
			confirmx('确认要'+msg+'启用通道3吗？', function(){
				$("#dataEntityId").val(id);
				$("#greenProjectorEnabled").val(greenProjectorEnabled);
				$("#searchForm").attr("action","${ctx}/signal/tblCollector/enableGreenProjector");
				$("#searchForm").submit();
			});

			return false;
		}
		function enableHorn(id,hornEnabled){
			var msg = hornEnabled=='0'?'不':'';
			confirmx('确认要'+msg+'启用音响吗？', function(){
				$("#dataEntityId").val(id);
				$("#hornEnabled").val(hornEnabled);
				$("#searchForm").attr("action","${ctx}/signal/tblCollector/enableHorn");
				$("#searchForm").submit();
			});

			return false;
		}
		function enableDisplay(id,displayEnabled){
			var msg = displayEnabled=='0'?'不':'';
			confirmx('确认要'+msg+'启用显示屏吗？', function(){
				$("#dataEntityId").val(id);
				$("#displayEnabled").val(displayEnabled);
				$("#searchForm").attr("action","${ctx}/signal/tblCollector/enableDisplay");
				$("#searchForm").submit();
			});

			return false;
		}
		function enableAttitude(id,attitudeEnabled){
			var msg = attitudeEnabled=='0'?'不':'';
			confirmx('确认要'+msg+'启用姿态传感器吗？', function(){
				$("#dataEntityId").val(id);
				$("#attitudeEnabled").val(attitudeEnabled);
				$("#searchForm").attr("action","${ctx}/signal/tblCollector/enableAttitude");
				$("#searchForm").submit();
			});

			return false;
		}

	</script>

	<ul class="nav nav-tabs">
			<li class="active"><a href="${ctx}/signal/tblCollector/listColumn?condition.model=14 & condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">标志牌列表</a></li>
			<shiro:hasPermission name="signal:tblColumn:add"><li><a href="#" onclick="editColumnItem('')">添加标志牌</a></li></shiro:hasPermission>
	</ul>
	<form:form id="searchForm" modelAttribute="form" action="${ctx}/signal/tblCollector/listColumn"
		method="post" class="breadcrumb form-search">
		<input id="pageNo" name="condition.page.pageNo" type="hidden" value="${form.condition.page.pageNo}"/>
		<input id="pageSize" name="condition.page.pageSize" type="hidden" value="${form.condition.page.pageSize}"/>
		<input id="dataEntityId" name="dataEntity.id" type="hidden" value=""/>
		<input id="enabled" name="dataEntity.enabled" type="hidden" value=""/>
		<input id="model" name="dataEntity.model" type="hidden" value="14"/>
<%--		<input id="model" name="dataEntity.model" type="hidden" value="${form.condition.model}"/>--%>
		<input id="simNo" name="dataEntity.simNo" type="hidden" value=""/>
		<input id="areaId" name="condition.areaId" type="hidden" value="${form.condition.areaId}"/>
		<input id="redProjectorEnabled" name="dataEntity.redProjectorEnabled" type="hidden" value=""/>
		<input id="greenProjectorEnabled" name="dataEntity.greenProjectorEnabled" type="hidden" value=""/>
		<input id="hornEnabled" name="dataEntity.hornEnabled" type="hidden" value=""/>
		<input id="displayEnabled" name="dataEntity.displayEnabled" type="hidden" value=""/>
		<input id="attitudeEnabled" name="dataEntity.attitudeEnabled" type="hidden" value=""/>

		<ul class="ul-form">
			<li><label>名称：</label>
				<form:input path="condition.name" htmlEscape="false" maxlength="128" class="input-medium"/>
			</li>
			<li class="btns"><input id="btnSubmit" class="btn btn-primary" type="submit" value="查询"/></li>
			<li class="clearfix"></li>
		</ul>
	</form:form>
	<sys:message content="${message}"/>
	<table id="contentTable" class="table table-striped table-bordered table-condensed">
		<thead>
			<tr>
				<th>序号</th>
				<th>设备编码</th>
				<th>名称</th>
				<th>型号</th>
				<th>地址</th>
				<th>实时状态</th>
				<th>故障状态</th>
				<th>交付时间</th>
				<th>更新时间</th>
				<th>质保时间</th>
				<th>质保状态</th>
				<th>标志牌状态</th>
				<th>调光状态</th>
				<th>通道3状态</th>
				<th>开关状态</th>
				<th>显示屏状态</th>
				<th>姿态状态</th>
				<th>路口状态</th>
				<shiro:hasAnyPermissions name="signal:brand:edit,signal:brand:delete">
					<th>操作</th>
				</shiro:hasAnyPermissions>
			</tr>
		</thead>
		<tbody>
		<c:forEach items="${form.condition.page.list}" var="tblColumn" varStatus="status">
			<tr>
				<td>${status.count}</td>
				<td><a href="#" onclick="editColumnItem('${tblColumn.id}')">${tblColumn.code}</a></td>
				<td  title="${tblColumn.name}">${tblColumn.name}</td>
				<td>${tblColumn.model}</td>
				<td  title="${tblColumn.address}">${tblColumn.address}</td>
				<td>${tblColumn.status==0 ? "在线" :"离线"}</td>
				<td>${tblColumn.failureStatus==0 ? "正常" :"故障"}</td>
				<td><fmt:formatDate value="${tblColumn.prodDeliveryTime}" pattern="yyyy-MM-dd"/></td>
				<td><fmt:formatDate value="${tblColumn.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
				<td><fmt:formatDate value="${tblColumn.prodEndTime}" pattern="yyyy-MM-dd"/></td>
				<td style="color:${tblColumn.prodWarrantyStatus==0?"green":tblColumn.prodWarrantyStatus==1?"red":"#e98a02"}">
						${tblColumn.prodWarrantyStatus==0?"正常":tblColumn.prodWarrantyStatus==1?"超期":"即将到期"}</td>
<%--				1--%>
				<shiro:hasPermission name="signal:brand:edit">
					<td>
						<c:if test='${tblColumn.enabled eq "0"}'>
							<a href="#" onclick="enableColumn('${tblColumn.id}','1')">未启用 </a>
						</c:if>
						<c:if test='${tblColumn.enabled eq "1"}'>
							<a href="#" onclick="enableColumn('${tblColumn.id}','0')">启用</a>
						</c:if>
					</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:brand:edit">
					<td>
						<c:if test='${tblColumn.enabled eq "0"}'>未启用</c:if>
						<c:if test='${tblColumn.enabled eq "1"}'>启用</c:if>
					</td>
				</shiro:lacksPermission>
					<%--				2--%>
				<shiro:hasPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.redProjectorEnabled eq "0"}'>
						<a href="#" onclick="enableRedProjector('${tblColumn.id}','1')">未启用 </a>
					</c:if>
					<c:if test='${tblColumn.redProjectorEnabled eq "1"}'>
						<a href="#" onclick="enableRedProjector('${tblColumn.id}','0')">启用</a>
					</c:if>
				</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.redProjectorEnabled eq "0"}'>未启用</c:if>
					<c:if test='${tblColumn.redProjectorEnabled eq "1"}'>启用</c:if>
				</td>
				</shiro:lacksPermission>
					<%--				3--%>
				<shiro:hasPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.greenProjectorEnabled eq "0"}'>
						<a href="#" onclick="enableGreenProjector('${tblColumn.id}','1')">未启用 </a>
					</c:if>
					<c:if test='${tblColumn.greenProjectorEnabled eq "1"}'>
						<a href="#" onclick="enableGreenProjector('${tblColumn.id}','0')">启用</a>
					</c:if>
				</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.greenProjectorEnabled eq "0"}'>未启用</c:if>
					<c:if test='${tblColumn.greenProjectorEnabled eq "1"}'>启用</c:if>
				</td>
				</shiro:lacksPermission>
					<%--				4--%>
				<shiro:hasPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.hornEnabled eq "0"}'>
						<a href="#" onclick="enableHorn('${tblColumn.id}','1')">未启用 </a>
					</c:if>
					<c:if test='${tblColumn.hornEnabled eq "1"}'>
						<a href="#" onclick="enableHorn('${tblColumn.id}','0')">启用</a>
					</c:if>
				</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblColumn:edit">
				<td>
					<c:if test='${tblColumn.id eq "0"}'>未启用</c:if>
					<c:if test='${tblColumn.id eq "1"}'>启用</c:if>
				</td>
				</shiro:lacksPermission>
					<%--				5--%>
				<shiro:hasPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.displayEnabled eq "0"}'>
						<a href="#" onclick="enableDisplay('${tblColumn.id}','1')">未启用 </a>
					</c:if>
					<c:if test='${tblColumn.displayEnabled eq "1"}'>
						<a href="#" onclick="enableDisplay('${tblColumn.id}','0')">启用</a>
					</c:if>
				</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.displayEnabled eq "0"}'>未启用</c:if>
					<c:if test='${tblColumn.displayEnabled eq "1"}'>启用</c:if>
				</td>
				</shiro:lacksPermission>
					<%--				6--%>
				<shiro:hasPermission name="signal:brand:edit">
				<td>
					<c:if test='${tblColumn.attitudeEnabled eq "0"}'>
						<a href="#" onclick="enableAttitude('${tblColumn.id}','1')">未启用 </a>
					</c:if>
					<c:if test='${tblColumn.attitudeEnabled eq "1"}'>
						<a href="#" onclick="enableAttitude('${tblColumn.id}','0')">启用</a>
					</c:if>
				</td>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:brand:edit">
				<td class="longContent">
					<c:if test='${tblColumn.attitudeEnabled eq "0"}'>未启用</c:if>
					<c:if test='${tblColumn.attitudeEnabled eq "1"}'>启用</c:if>
				</td>
				</shiro:lacksPermission>
				<shiro:hasAnyPermissions name="signal:brand:edit">
				<td>
				<a href="${ctx}/signal/tblAlarmConfig/listColumnHistory?condition.collectorId=${tblColumn.id}&condition.model=${tblColumn.model}&condition.collectorName=${tblColumn.name}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.startDate=&condition.endDate=">历史</a>
				<a href="${ctx}/signal/tblAlarmConfig/listFailureHistory?condition.collectorId=${tblColumn.id}&condition.model=${tblColumn.model}&condition.collectorName=${tblColumn.name}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.startDate=&condition.endDate=">故障</a>
				</td>
				</shiro:hasAnyPermissions>

				<shiro:hasAnyPermissions name="signal:brand:edit,signal:brand:delete">
					<td>
					<a href="#" onclick="editColumnItem('${tblColumn.id}')">编辑</a>
					<a  href="${ctx}/signal/tblAlarmConfig/pillarDetail?condition.collectorId=${tblColumn.id}&condition.model=${tblColumn.model}" >配置</a>&nbsp;
					<a href="#" onclick="deleteItem('${tblColumn.id}','${tblColumn.enabled}','${tblColumn.redProjectorEnabled}','${tblColumn.greenProjectorEnabled}','${tblColumn.hornEnabled}','${tblColumn.displayEnabled}','${tblColumn.attitudeEnabled}')">删除</a>
					</td>
				</shiro:hasAnyPermissions>

		</c:forEach>
		</tbody>
	</table>
	<div class="pagination">${form.condition.page}</div>
</layout:default>