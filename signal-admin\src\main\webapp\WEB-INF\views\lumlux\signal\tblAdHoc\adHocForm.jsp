<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8" %>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>

<layout:default title="信号机表管理" bodyId="collectorId">
	<style>
		.input-width-20{
			width:20px;
			text-align: center;
		}
		.float-left{
			padding-left:2px;
			float:left;
		}
		.modal{
			left: 40%;
		}
	</style>
	<script type="text/javascript">

		$(document).ready(function() {
			$("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});

			$(".input-width-20").blur(function(){
				if(this.value.length==0){
					this.value="00";
				}else if(this.value.length==1){
					this.value= "0" + this.value;
				}else if(this.value.length==2){
					if($(this).hasClass("type-hour")){
						if(parseInt(this.value)<0){
							this.value="00";
						}else if(parseInt(this.value)>23){
							this.value="23";
						}
					}else if($(this).hasClass("type-minute")){
						if(parseInt(this.value)<0){
							this.value="00";
						}else if(parseInt(this.value)>59){
							this.value="59";
						}
					}
				}
			});

			$("#roadeImagePreview img").click(function(){
				var html = "";
				$(".modal-body").html();
			});

			$("#collectorModel").change(switchCollectorBoard);

			initDataFormatter();
			initIccidValidator();
			switchCollectorBoard();

			// 将版本号置空
			document.getElementById("dataEntity.version").value = "";
		});

		function selectLocation(){
			$.jBox("iframe:${pageContext.request.contextPath}/selectCollectorLocation.jsp",{
				title: "选择经纬度",
				width: 600,
				height: 400,
				buttons: { '关闭': 1 ,'确定':2},
				submit: function (v, h, f) {
					/*if (v==1) {
					 return true; // close the window
					 } else {
					 return false;
					 }*/

				},
				loaded:function(h){

				}
			});
		}

		function  selectLocationCallBack(longitude,latitude){
			$("#longitude").val(longitude);
			$("#latitude").val(latitude);
		}

		function getVersion(button){
			button.disabled = true;
			button.value = "等待中";
			var param = {"id": document.getElementById("dataEntity.id").value};
			this.setTimeout(_=> $.ajax({
				url:"${ctx}/signal/tblCollector/getVersion1",
				type:"post",
				data: JSON.stringify(param),
				contentType: "application/json; charset=utf-8",dataType: "json",
				success:function(result){
					if(result.data!=null){
						document.getElementById("dataEntity.version").value = result.data;
						$("#info-span")[0].style.color="green";
						$("#info-span").html("获取成功");
					}else{
						$("#info-span")[0].style.color="red";
						$("#info-span").html("实时获取失败");
					}
					button.value = "读 取";
					button.disabled = false;
				},
				error: function (e) {
					console.log(e);
					$("#info-span")[0].style.color="red";
					$("#info-span").html("请求失败");
					button.value = "读 取";
					button.disabled = false;
				}
			}), 3000);
		}
		
		function collectorSubmit(){
			var isEdit = ${form.dataEntity.isEdit};
			if(isEdit == 0)
			{
				$("#inputForm").attr("action","${ctx}/signal/tblCollector/add");
			}
			$("#inputForm").submit();
		}

		// 将Date转化为"yyyy-MM-dd"或"yyyy-MM"格式
		function dateFormat(date, requireDays) {
			return requireDays ? date.getFullYear() + "-" +
					(date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth()) + "-" +
					(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) :
					date.getFullYear() + "-" + (date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth());
		}

		function initDataFormatter() {
			jQuery.validator.addMethod('dateVaildate', value => new RegExp(
					"^(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-" +
					"(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" +
					"(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|1[0-9]|2[0-8]))))|" +
					"((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
					"((0[48]|[2468][048]|[13579][26])00))-02-29)$").test(value) || value == '')

			jQuery.extend(jQuery.validator.messages, { dateValidate: "格式要求：年-月-日（例如：2023-06-01）" });

			// 修改安装时间格式

			let prodDeliveryTime = "${form.dataEntity.prodDeliveryTime}";
			let prodEndTime = "${form.dataEntity.prodEndTime}";
			let deviceInstallTime = "${form.dataEntity.deviceInstallTime}";
			document.getElementById("dataEntity.deviceInstallTime").value = deviceInstallTime ? dateFormat(new Date(deviceInstallTime), true) : '';
			document.getElementById("dataEntity.prodDeliveryTime").value = prodDeliveryTime ? dateFormat(new Date(prodDeliveryTime), true) : '';
			document.getElementById("dataEntity.prodEndTime").value = prodEndTime ? dateFormat(new Date(prodEndTime), true) : '';
		}


		function initIccidValidator() {
			let result = {};
			jQuery.validator.addMethod('iccidVaildate', value => {
				$.ajax({
					url:"${ctx}/signal/tblCollector/checkedIccid?iccid=" + value,
					type:"post",
					async: false,
					success: data => result = data,
					error: _ => result = {
						"status": 500,
						"data": "58785785278210562054",
						"message": { "error": "Iccid校验失败" }
					}
				})
				return value != '' && ( value == "${form.dataEntity.iccid}" || result.status == 200 )
			})
			jQuery.extend(jQuery.validator.messages, { iccidVaildate: _ =>  {
					return result.message.message
				}});
		}

		function editItemConfig(id, model) {
			clickOpenClose(true);
			setTimeout(_=> window.location.href = "${ctx}/signal/tblCollector/adHocDetailConfig?id=" + id + "&model=" + model, 300);
		}

		function switchCollectorBoard() {
			if ($("#collectorModel").val() == 16)
				$("#boardQuantity").show();
			else
				$("#boardQuantity").hide();
		}

		function clickOpenClose(leftIsShown) {
			if(parent.$('#left').css('display') == (leftIsShown ? 'block' : 'none'))
				parent.$("#openClose").click();
		}
	</script>

	<ul class="nav nav-tabs">
		<li><a href="${ctx}/signal/tblCollector/list?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.model=15,16">信号机列表</a></li>
		<li class="active">
			<a>
				<shiro:hasPermission name="signal:tblAdHoc:edit">${not empty form.dataEntity.id?'修改信号机':'添加信号机'}</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">查看信号机</shiro:lacksPermission></a></li>
			</a>
		</li>

	</ul><br/>
	<form:form id="inputForm" modelAttribute="form" action="${ctx}/signal/tblCollector/edit?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" method="post" class="form-horizontal">
		<form:hidden path="dataEntity.id"/>
		<form:hidden path="condition.model" value="15"/>
		<form:hidden path="condition.page.pageNo"/>
		<form:hidden path="condition.page.pageSize"/>
		<!-- todo others condition -->
		<form:hidden path="condition.name"/>
		<form:hidden path="dataEntity.isEdit"/>
		<form:hidden path="dataEntity.ledCount" value="0"/>
		<form:hidden path="dataEntity.dimmingStartTime" value="1970-01-01"/>
		<form:hidden path="dataEntity.dimmingEndTime" value="1970-01-01"/>
		<form:hidden path="dataEntity.dimmingControlStatus" value="0"/>

		<sys:message content="${message}"/>
		<sys:message content="${ifExistMsg}" type="error"/>

		<div class="control-group">
			<label class="control-label">名称：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.name" htmlEscape="false" maxlength="20" class="input-xlarge required"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.name}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">型号：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:select id="collectorModel" path="dataEntity.model"  cssStyle="width: 290px"  class="input-xlarge required" >
						<form:option value="">请选择型号</form:option>
						<c:forEach items="${collectorType}" var="sysDictionaries">
							<c:if test="${sysDictionaries.value == '15' || sysDictionaries.value == '16'}">
								<form:option value="${sysDictionaries.value}">${sysDictionaries.description}</form:option>
							</c:if>
						</c:forEach>
					</form:select>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.model}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		
		<div class="control-group">
			<label class="control-label">启用状态：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:radiobutton path="dataEntity.enabled" value="0" checked="true"/>未启用
					<form:radiobutton path="dataEntity.enabled"   value="1"/>启用&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<c:if test="${form.dataEntity.enabled == '0'}">
						<span>未启用</span>
					</c:if>
					<c:if test="${form.dataEntity.enabled == '1'}">
						<span>启用</span>
					</c:if>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">物联网卡号(ICCID)：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.iccid" htmlEscape="false" maxlength="20" minlength="20" class="input-xlarge required iccidVaildate"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.iccid}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">编码：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.code" htmlEscape="false" maxlength="11" minlength="11" class="input-xlarge required number"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.code}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div id="boardQuantity" class="control-group" style="display: none;">
			<label class="control-label">信号灯数量：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.boardQuantity" htmlEscape="false" value="4"
								class="input-xlarge required number routerValidate"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.boardQuantity}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">经纬度：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input id="longitude" path="dataEntity.longitude" htmlEscape="false" maxlength="16" class="input-small required number"/>
					<form:input id="latitude" path="dataEntity.latitude" htmlEscape="false" maxlength="16" class="input-small required number"/>
					<span class="help-inline"><font color="red">*</font> </span>
					<input class="btn btn-primary" type="button" value="选 择" onclick="selectLocation()"/>
					<div id="tip"></div>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>经度${form.dataEntity.longitude}</span>&nbsp;<span>纬度${form.dataEntity.latitude}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">地址：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.address" htmlEscape="false" class="input-xlarge required"  maxlength="20" />
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.address}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">设备安装时间：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.deviceInstallTime" htmlEscape="false" class="input-xlarge dateVaildate" maxlength="10"/>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span><fmt:formatDate value="${form.dataEntity.deviceInstallTime}" pattern="yyyy-MM-dd"/></span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">制造商：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:select path="dataEntity.supplierId"  cssStyle="width: 290px"  class="input-xlarge required" >
						<form:option value="">请选择制造商</form:option>
						<form:options items="${suppliers}" itemValue="id" itemLabel="name"  ></form:options>
					</form:select>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.supplierName}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">管理单位：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:select path="dataEntity.office.id"    class="input-xlarge required" cssStyle="width: 290px" >
						<form:option value="">请选择管理单位</form:option>
						<form:options items="${offices}" itemValue="id" itemLabel="name"></form:options>
					</form:select>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.office.name}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">维护单位：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:select path="dataEntity.supportId"   cssStyle="width: 290px"  class="input-xlarge required">
						<form:option value="">请选择维护单位</form:option>
						<form:options items="${supports}" itemValue="id" itemLabel="name"></form:options>
					</form:select>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.supportName}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">所属区域：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<sys:treeselect id="area" name="dataEntity.area.id" value="${form.dataEntity.area.id}" labelName="area.name" labelValue="${form.dataEntity.area.name}"
									title="区域" url="/signal/tblCollector/treeData" cssClass="required" allowClear="true" notAllowSelectParent="false" dataMsgRequired="必填信息"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.area.name}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group" style="display:none;">
			<label class="control-label">是否通知：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:select path="dataEntity.isInform"  cssStyle="width: 290px"  class="input-xlarge required" >
						<form:option value="0">否</form:option>
						<form:option value="1">是</form:option>
					</form:select>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.isInform}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group" style="display: none;">
			<label class="control-label">版本号：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.version" htmlEscape="false" class="input-xlarge" readonly="true" maxlength="20"/>&nbsp;
					<input class="btn btn-primary" type="button" value="读 取" onclick="getVersion(this)"/>
					<span style="position: relative;top: 2px;" id="info-span"></span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.version}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">产品交付时间：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.prodDeliveryTime" htmlEscape="false" class="input-xlarge dateVaildate" maxlength="10"/>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span><fmt:formatDate value="${form.dataEntity.prodDeliveryTime}" pattern="yyyy-MM-dd"/></span>
				</shiro:lacksPermission>
			</div>
		</div>

		<div class="control-group">
			<label class="control-label">产品质保时间：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:input path="dataEntity.prodEndTime" htmlEscape="false" class="input-xlarge dateVaildate required" maxlength="10"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span><fmt:formatDate value="${form.dataEntity.prodEndTime}" pattern="yyyy-MM-dd"/></span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div id="collectorPic" class="control-group">
			<label class="control-label">路口图片：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:hidden id="roadeImage" path="dataEntity.pictureUrl" htmlEscape="false" maxlength="255" class="input-xlarge"/>
					<sys:ckfinder input="roadeImage" type="images" uploadPath="/photo" selectMultiple="true" maxWidth="100" maxHeight="100" title="路口图片"/>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<img src="${form.dataEntity.pictureUrl}" style="width: 200px" height="150px">
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">备注：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblAdHoc:edit">
					<form:textarea path="dataEntity.remarks" rows="4" cols="20"  maxlength="256" style="resize:none;width:270px;" />
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblAdHoc:edit">
					<span>${form.dataEntity.remarks}</span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="form-actions" style="padding-left:130px;">
			<shiro:hasPermission name="signal:tblAdHoc:edit">
				<input id="btnSubmit" onclick="collectorSubmit()" class="btn btn-primary" type="button" value="保 存"/>&nbsp;
				<c:if test='${empty form.dataEntity.id}'>
					<a id="reset" class="btn btn-primary" href="">重置</a>&nbsp;
				</c:if>
				<c:if test='${not empty form.dataEntity.id}'>
					<a class="btn btn-primary" href="#" onclick="editItemConfig('${form.dataEntity.id}', '${form.dataEntity.model}')">详细配置</a>&nbsp;
				</c:if>
			</shiro:hasPermission>

			<%-- 
			<shiro:hasPermission name="signal:tblAlarm:view">
				<c:if test='${not empty form.dataEntity.id and form.dataEntity.model >= 10}'>
					<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/secondDetail?condition.collectorId=${form.dataEntity.id}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" >参数配置</a>&nbsp;
				</c:if>
			</shiro:hasPermission> 
			--%>

			<a id="btnCancel" class="btn" onclick="javascript:window.history.back();return false;">返回</a>
		</div>
	</form:form>
</layout:default>