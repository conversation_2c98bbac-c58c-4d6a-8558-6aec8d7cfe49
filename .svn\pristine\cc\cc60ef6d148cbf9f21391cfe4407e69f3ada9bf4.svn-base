package com.lumlux.signal.controller;

import com.heinqi.yangtes.base.web.AsyncResponseData;
import com.heinqi.yangtes.jee.commons.web.BaseController;
import com.heinqi.yangtes.jee.modules.sys.utils.UserUtils;
import com.lumlux.commons.entity.*;
import com.lumlux.signal.entity.*;

import com.lumlux.signal.service.*;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.*;

/**
 * <p>Title: TblAdHOCController.java</p>
 * <p>Description: TblAdHOCController单表表单</p>
 * <p>Author: <PERSON> <PERSON></p>
 * <p>Date: 2024/5/21</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
@RestController
@RequestMapping(value = "${adminPath}/signal/adHOCScheme")
//@Validated
public class TblAdHOCSchemeController extends BaseController {

    @Autowired
    private DaemonService daemonService;

    @Autowired
    private TblCollectorService tblCollectorService;


    @Autowired
    private TblAdHOCLightDetailService adHOCLightDetailService;
    @Autowired
    private TblAdHOCSchemeService adHOCSchemeService;


    //    @RequiresPermissions("signal:tblAdHOCScheme:getSchemeInfo")

    @RequestMapping(value = "getSchemeInfo")
    public AsyncResponseData.ResultData getSchemeInfo(@RequestParam("collectorId")String collectorId,@RequestParam("schemeNo")String schemeNo){
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();


        if (collectorId ==null || schemeNo ==null) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        TblCollector collector = tblCollectorService.get(collectorId);
        if (null == collector) {
            messages.put("message", "该集中器不存在");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }

        List<TblAdHOCScheme> tblAdHOCScheme = adHOCSchemeService.getSchemeInfo(collectorId,schemeNo);

       if (tblAdHOCScheme == null) {
           messages.put("message", "该自组网方案不存在");
           data.setMessages(messages);
           data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
           return data;
        }
        List<TblSignalLightDetail> signalLightDetails = adHOCLightDetailService.getLightDetailByCollectorIdAndSchemeNo(collectorId,schemeNo);
        if (signalLightDetails == null || signalLightDetails.size() == 0){
            messages.put("message", "该自组网方案下没有信号灯详情信息");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;

        }
        QuerySpecificLightValueBean  specificLightValueBean = new QuerySpecificLightValueBean();
        AlterableLightValueBean  alterableLightValueBean = new AlterableLightValueBean();
         BeanUtils.copyProperties(tblAdHOCScheme,specificLightValueBean);

         List<AlterableLightValueBean> alterableLightValueBeans = new ArrayList<>();
//        List<AlterableLightValueBean> alterableLightValueBeans = specificLightValueBean.getAlterableLightValueBeans();
        for (TblSignalLightDetail tblSignalLightDetail : signalLightDetails) {

             BeanUtils.copyProperties(tblSignalLightDetail,alterableLightValueBean);
            alterableLightValueBeans.add(alterableLightValueBean);
         }
          specificLightValueBean.setAlterableLightValueBeans(alterableLightValueBeans);

        messages.put("message", "自组网方案查询成功");
        data.setMessages(messages);
        data.setData(specificLightValueBean);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }


    /**
     * 下放方案协议包
     *
     * @param slvBean
     * @return
     */
//    @RequiresPermissions("signal:tblAdHOCScheme:putSchemeInfo")
    @RequestMapping(value = "putSchemeInfo")
    public AsyncResponseData.ResultData putSchemeInfo(@RequestBody SpecificLightValueBean slvBean) throws InterruptedException {

        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();


        TblCollector collector = tblCollectorService.get(slvBean.getId());
        if (null == collector) {
            messages.put("message", "该集中器不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }

        //初始化putflag为0
        tblCollectorService.updatePutFlag(collector.getId(), 0);

        Integer count = Integer.parseInt(collector.getCounter());
        String schemeNo = slvBean.getSchemeNo();
        if (null == schemeNo) {
            messages.put("message", "方案编号不能为空");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        TblAdHOCScheme adHOCScheme=adHOCSchemeService.getAdHOCScheme(schemeNo,collector.getId());

        if (adHOCScheme == null) {
            messages.put("message", "该方案不存在，请创建");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        List<AlterableLightValueBean> lvBeans = slvBean.getLightValueBeans();

        if (lvBeans == null || lvBeans.size() == 0) {
            messages.put("message", "该方案没有配置灯详细参数");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }

        for (int i = 0; i < lvBeans.size(); i++) {

            AlterableLightValueBean alvBean = lvBeans.get(i);
            LightValueBean lightValueBean = new LightValueBean();
            BeanUtils.copyProperties(slvBean, lightValueBean);
            BeanUtils.copyProperties(alvBean, lightValueBean);
            lightValueBean.setCount(count);//包序列号
            lightValueBean.setLightTotal(collector.getBoardQuantity());//灯总数
            //多线程处理返回
            daemonService.putAdHOCShemeConfig(collector.getCode(), lightValueBean,collector.getModel());//下放组网方案
            Thread.sleep(1000);
        }
             try {
                //下放
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                      return "0";
                    }
                });
                 count++;
                 if (count == 255) {
                     count = 1;
                 }
                 //更新包序列
                 tblCollectorService.updateCount(count,slvBean.getId());

                 String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "自组网下放方案失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        return null;
    }
//    @RequiresPermissions("signal:AdHOCScheme:addSchemeInfo")
    @RequestMapping(value = "addSchemeInfo")
    public AsyncResponseData.ResultData add(@Valid @RequestBody AddSpecificLightValueBean addSpecificLightValueBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        TblCollector collector = tblCollectorService.get(addSpecificLightValueBean.getId());
        if (null == collector) {
            messages.put("message", "该集中器不存在");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }

        if (adHOCSchemeService.selectAdHOCSchemeByCollectorIdAndSchemeNo(addSpecificLightValueBean.getId(),addSpecificLightValueBean.getSchemeNo())>0) {
            messages.put("message", "该方案已存在");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }


        List<AlterableLightValueBean> lvBeans = addSpecificLightValueBean.getLightValueBeans();

        if (lvBeans == null || lvBeans.size() == 0) {
            messages.put("message", "该方案没有配置灯详细参数");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }

       for (AlterableLightValueBean lvBean : lvBeans) {
            if (adHOCLightDetailService.getLightDetail(addSpecificLightValueBean.getId(), addSpecificLightValueBean.getSchemeNo(),lvBean.getLightNumber()) > 0) {
                messages.put("message", "该灯下已经存在该方案");
                data.setMessages(messages);
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                return data;
            }

        }
     /*   if (lvBeans.size() !=collector.getBoardQuantity()) {
            messages.put("message", "灯数量与采集器数量不一致");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }*/
        //添加
      adHOCSchemeService.insertScheme(addSpecificLightValueBean);

        messages.put("message", "自组网方案添加成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }
    //    @RequiresPermissions("signal:tblAdHOCScheme:updateSchemeInfo")

    @RequestMapping(value = "updateSchemeInfo")
    public AsyncResponseData.ResultData updateSchemeInfo(@RequestBody AddSpecificLightValueBean addSpecificLightValueBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        adHOCSchemeService.updateSchemeInfo(addSpecificLightValueBean);

        messages.put("message", "自组网方案更新成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }
    //    @RequiresPermissions("signal:tblAdHOCScheme:deleteSchemeInfo")

    @RequestMapping(value = "deleteSchemeInfo")
    public AsyncResponseData.ResultData deleteSchemeInfo(@RequestParam("collectorId")String collectorId, @RequestParam("schemeNo")String schemeNo) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        if (collectorId==null||schemeNo== null) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        TblAdHOCScheme adHOCScheme = adHOCSchemeService.getAdHOCScheme(schemeNo, collectorId);
        if (adHOCScheme == null) {
            messages.put("message", "该自组网方案不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;

        }

        adHOCSchemeService.deleteSchemeInfo(collectorId, schemeNo);


        messages.put("message", "自组网方案删除成功");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        return data;

    }

    /**
     * -所有方案下放成功，给IOT统一下放开始按钮
     * @param portableSchemeBean
     * @return
     */
    @RequestMapping(value = "putPortableSchemeLevelInfo")
    public AsyncResponseData.ResultData putPortableSchemeLevelInfo(@RequestBody PortableSchemeBean portableSchemeBean) {

        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();


        TblCollector collector = tblCollectorService.get(portableSchemeBean.getId());
        if (null == collector) {
            messages.put("message", "该集中器不存在");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        //初始化putflag为0
        tblCollectorService.updatePutFlag(collector.getId(), 0);

        Integer count = Integer.parseInt(collector.getCounter());
        String schemeNo = portableSchemeBean.getSchemeNo();
        if (null == schemeNo) {
            messages.put("message", "方案编号不能为空");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
        TblAdHOCScheme adHOCScheme=adHOCSchemeService.getAdHOCScheme(schemeNo,collector.getId());

        if (adHOCScheme == null) {
            messages.put("message", "该方案不存在，请创建");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            return data;
        }
            //多线程处理返回
            try {
                //下放
                daemonService.putPortableSchemeLevelInfo(collector.getCode(),portableSchemeBean.getSchemeNo(),count);//下放组网方案
                Thread.sleep(1000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                count++;

                if (count == 255) {
                    count = 1;
                }
                //更新包序列
                tblCollectorService.updateCount(count,collector.getId());
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "自组网下放方案失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
            //灯详情更新
        /*    TblSignalLightDetail signalLightDetail =new TblSignalLightDetail();
            BeanUtils.copyProperties(alvBean, signalLightDetail);
            signalLightDetail.setSchemeNo(schemeNo);
            signalLightDetail.setUpdateDate(new Date());
            signalLightDetail.setUpdateBy(UserUtils.getUser());
            signalLightDetail.setCollectorId(slvBean.getCollectorId());
            adHOCLightDetailService.updateAdHOCLightDetail(signalLightDetail);*/

//        }

      /*  //方案更新
        TblAdHOCScheme tblAdHOCScheme = new TblAdHOCScheme();
//        BeanUtils.copyProperties(slvBean, tblAdHOCScheme);
        tblAdHOCScheme.setUpdateDate(new Date());
        tblAdHOCScheme.setUpdateBy(UserUtils.getUser());
        adHOCSchemeService.updateAdHOCScheme(tblAdHOCScheme);


        messages.put("message", "自组网下放方案成功") ;
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
        data.setMessages(messages);*/
        return null;
    }

    /**
     * 下放調光配置
     *
     * @param dimmingBean
     * @return
     */
    @RequestMapping(value = "putDimming", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData putDimming(@RequestBody CommonDimmingBean dimmingBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        if (null == dimmingBean.getId()){
            messages.put("message", "主控制器id不能为空");
            data.setMessages(messages);
            return data;
        }
        int controlStatus = dimmingBean.getDimmingParam().getControlStatus();
        String controlStatusStr = String.valueOf(controlStatus);

        if (!controlStatusStr.matches("^[01]*$")) {
            System.out.println("调光类型必须输入0或者1的整数");
        }

        TblCollector collector = tblCollectorService.get(dimmingBean.getId());

        if (collector != null) {
            Integer count = Integer.parseInt(collector.getCounter());

            //初始化putflag为0
            tblCollectorService.updatePutFlag(collector.getId(), 0);

            DimmingParam dimmingParam = dimmingBean.getDimmingParam();

            if (null == dimmingParam  || null ==dimmingParam.getEndTime() || null==dimmingParam.getStartTime() || null == dimmingParam.getControlStatus()){
                messages.put("message", "下放调光配置参数必须完整的给值");
                data.setMessages(messages);
                return data;
            }
            if (dimmingParam != null && dimmingParam.getEndTime() != null && dimmingParam.getStartTime() != null) {
                try {
                    daemonService.putDimmingPortableConfig(collector.getCode(),count, dimmingParam);//下放调光配置参数
                    tblCollectorService.updateDimmingParam(dimmingParam, collector.getId()); //保存调光配置参数
               /* } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }*/
//            try {
                    Thread.sleep(3000);
    /*    } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }*/

                    ExecutorService executor = Executors.newSingleThreadExecutor();
                    Future<String> future = executor.submit(new Callable<String>() {
                        @Override
                        public String call() throws Exception {
//                Thread.sleep(3000);
                            TblCollector tblVariableVersion = tblCollectorService.get(collector.getId());
                            String version = tblVariableVersion.getPutFlag();
                            if (version != null) {
                                return version;
                            }
                            return null;
                        }
                    });
                    count++;

                    if (count == 255) {
                        count = 1;
                    }
                    //更新包序列
                    tblCollectorService.updateCount(count,collector.getId());
                    // 获取异步操作的结果
//        try {
                    String result = future.get();
                    if (result != null) {
                        if ("1".equals(result)) {
                            messages.put("message", "调光配置参数下放成功");
                            data.setMessages(messages);
                            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                            return data;
                        } else {
                            messages.put("message", "调光配置参数下放失败") ;
                            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                            data.setMessages(messages);
                            return data;

                        }
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                }
            }
           /* if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCountAndVersion(count,collector.getId(),0);*/

        }


    /*    messages.put("message", "无此控制器，请确认");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);*/
        return null;
    }

    @RequestMapping(value = "putOnClick", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData putOnClick(@RequestBody OnclickBean onclickBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        if (null == onclickBean.getId()){
            messages.put("message", "主控制器id不能为空");
            data.setMessages(messages);
            return data;
        }
        int onclickParameter = onclickBean.getOnclickParameter();



        TblCollector collector = tblCollectorService.get(onclickBean.getId());

        if (collector != null) {
            Integer count = Integer.parseInt(collector.getCounter());

            //初始化putflag为0
            tblCollectorService.updatePutFlag(collector.getId(), 0);

                try {
                    daemonService.putOnClickConfig(collector.getCode(), onclickParameter);//下放调光配置参数
//                    tblCollectorService.updateDimmingParam(dimmingParam, collector.getId()); //保存调光配置参数
               /* } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }*/
//            try {
                    Thread.sleep(3000);
    /*    } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }*/

                    ExecutorService executor = Executors.newSingleThreadExecutor();
                    Future<String> future = executor.submit(new Callable<String>() {
                        @Override
                        public String call() throws Exception {
//                Thread.sleep(3000);
                            TblCollector tblVariableVersion = tblCollectorService.get(collector.getId());
                            String version = tblVariableVersion.getPutFlag();
                            if (version != null) {
                                return version;
                            }
                            return null;
                        }
                    });
                    count++;

                    if (count == 255) {
                        count = 1;
                    }
                    //更新包序列
                    tblCollectorService.updateCount(count,collector.getId());
                    // 获取异步操作的结果
//        try {
                    String result = future.get();
                    if (result != null) {
                        if ("1".equals(result)) {
                            messages.put("message", "调光配置参数下放成功");
                            data.setMessages(messages);
                            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                            return data;
                        } else {
                            messages.put("message", "调光配置参数下放失败") ;
                            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
                            data.setMessages(messages);
                            return data;

                        }
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                }

           /* if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCountAndVersion(count,collector.getId(),0);*/

        }


    /*    messages.put("message", "无此控制器，请确认");
        data.setMessages(messages);
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);*/
        return null;
    }


}