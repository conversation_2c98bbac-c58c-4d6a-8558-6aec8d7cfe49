package com.lumlux.commons.dao;

import com.heinqi.yangtes.jee.base.AbstractCondition;
import com.heinqi.yangtes.jee.commons.persistence.CrudDao;
import com.heinqi.yangtes.jee.commons.persistence.annotation.MyBatisDao;
import com.lumlux.commons.entity.TblFailure;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface TblColumnFailureDao extends CrudDao<TblFailure> {

    public void deleteBatch(@Param("ids") List<String> ids);

    public void updateStatus(String status,String id);
    
    public List<TblFailure> getFailureHistory(AbstractCondition<TblFailure> condition);

    public List<TblFailure> getFailureHistorys(AbstractCondition<TblFailure> condition);

    public void deleteFailure(@Param("collectorId")String collectorId);


    List<TblFailure> findAllFailure(@Param("collectorId")String collectorId);


    List<TblFailure> insertFailure(@Param("list")List<TblFailure> failures);


    List<TblFailure>  byIdFailure(@Param("collectorId") String collectorId);

    Integer listNumber(@Param("collectorId")String collectorId);


}