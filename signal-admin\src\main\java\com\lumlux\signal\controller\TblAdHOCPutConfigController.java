package com.lumlux.signal.controller;

import com.heinqi.yangtes.base.web.AsyncResponseData;
import com.heinqi.yangtes.jee.commons.web.BaseController;
import com.lumlux.commons.entity.TblCollector;
import com.lumlux.signal.entity.*;
import com.lumlux.signal.service.*;
import com.lumlux.signal.util.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <p>Title: TblAdHOCController.java</p>
 * <p>Description: TblAdHOCController单表表单</p>
 * <p>Author: <PERSON> <PERSON></p>
 * <p>Date: 2024/6/4</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
@RestController
@RequestMapping(value = "${adminPath}/signal/adHOCPutConfig")
public class TblAdHOCPutConfigController extends BaseController {


    @Autowired
    private TblCollectorService tblCollectorService;

    @Autowired
    private TblAdHOCService adHOCService;
    @Autowired
    private TblAdHOCLightDetailService adHOCLightDetailService;
    @Autowired
    private TblAdHOCSchemeService adHOCSchemeService;
    @Autowired
    private TblAdHOCLightInfoTempService tblAdHOCLightInfoTempService;

    @Autowired
    private DaemonService daemonService;

    //    @RequiresPermissions("signal:adHOCPutConfig:putAdHOCHeartBeat")
    @RequestMapping(value = "putAdHOCHeartBeat")
    public AsyncResponseData.ResultData putAdHOCHeartBeat(@RequestBody PutAdHOCHeartBeatBean putAdHOCHeartBeatBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();


        TblCollector collector = tblCollectorService.get(putAdHOCHeartBeatBean.getId());


        if (collector != null) {

            daemonService.putAdHOCHeartBeat(collector.getCode(), putAdHOCHeartBeatBean);

            messages.put("message", "控制器心跳参数设置成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }

        return null;
    }

    /**
     * 主控通过扫描空中设备
     * @param putAdHOCQueryInfoBean
     * @return
     */
    //    @RequiresPermissions("signal:adHOCPutConfig:putAdHOCQueryInfo")
    @RequestMapping(value = "putAdHOCQueryInfo")
    public AsyncResponseData.ResultData putAdHOCQueryInfo(@RequestBody PutAdHOCQueryInfoBean putAdHOCQueryInfoBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        TblCollector collector = tblCollectorService.get(putAdHOCQueryInfoBean.getId());

        if (collector != null) {
            Integer count = Integer.parseInt(collector.getCounter());

            try {
                //下放
                daemonService.putAdHOCQueryInfo(collector.getCode(),count, putAdHOCQueryInfoBean);//a2
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放获取设备信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,putAdHOCQueryInfoBean.getId());

            messages.put("message", "下放获取设备信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }


        return null;
    }
    //    @RequiresPermissions("signal:adHOCPutConfig:putAdHOCFollowControllerInfo")

    /**
     * 平台操作按钮//移动式信号灯和自组网
     * @param collectorId
     * @param followLightAdress
     * @return
     */
    @RequestMapping(value = "putAdHOCFollowControllerInfo")
    public AsyncResponseData.ResultData putAdHOCFollowControllerInfo(@RequestParam("collectorId")String  collectorId,@RequestParam("followLightAdress")String  followLightAdress,@RequestParam("status")String status) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        byte[] followLightAdressBytes = NumberUtils.hexStringToByteArray(followLightAdress);

        ArrayList<Byte> followLightAdressBytesList = new ArrayList<>();
        for (byte  i=0; i<followLightAdressBytes.length; i++){
            followLightAdressBytesList.add(followLightAdressBytes[i]);
        }

        TblCollector collector = tblCollectorService.get(collectorId);

        if (collector != null) {
            int count =Integer.parseInt( collector.getCounter());
            try {
                //下放
                daemonService.putAdHOCFollowControllerInfo(collector.getCode(),count, followLightAdressBytesList,status,collector.getModel());//a4
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放获取从控设备信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }

            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,collectorId);

            messages.put("message", "下放获取从控设备信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }


        return null;
    }

    /**
     * 移动式信号灯和自组网下放
     *配置灯号和灯类型，平台操作按钮：配置灯号数字和选择灯类型，下发
     * @param lightNumberInfoBean
     * @return
     */
    //    @RequiresPermissions("signal:adHOCPutConfig:putMacInfo")
    @RequestMapping(value = "putMacInfo")
    public AsyncResponseData.ResultData putMacInfo(@RequestBody PutAdHOCLightNumberInfoBean lightNumberInfoBean) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();


        TblCollector collector = tblCollectorService.get(lightNumberInfoBean.getId());


        String followLightAdress = lightNumberInfoBean.getFollowLightAdress();

        byte[] followLightAdressBytes = NumberUtils.hexStringToByteArray(followLightAdress);

        ArrayList<Byte> followLightAdressBytesList = new ArrayList<>();
        for (byte  i=0; i<followLightAdressBytes.length; i++){
            followLightAdressBytesList.add(followLightAdressBytes[i]);
        }
        String masterLightAdress = lightNumberInfoBean.getMasterLightId();
        if (masterLightAdress.length() < 20) {
            masterLightAdress = String.format("%20s", masterLightAdress).replace(' ', '0');
        }

        byte[] masterLightAdressBytes = NumberUtils.hexStringToByteArray(masterLightAdress);

        ArrayList<Byte> masterLightAdressBytesList = new ArrayList<>();
        for (byte  i=0; i<masterLightAdressBytes.length; i++){
            masterLightAdressBytesList.add(masterLightAdressBytes[i]);
        }
        if (collector != null) {
            int count =Integer.parseInt( collector.getCounter());
            try {
                //下放
                daemonService.putMacInfo(collector.getCode(), count,lightNumberInfoBean.getLightNumber(),lightNumberInfoBean.getLightType(),followLightAdressBytesList,masterLightAdressBytesList,collector.getModel());//a5.ab
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放定位信号灯信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }

            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,lightNumberInfoBean.getId());



            messages.put("message", "下放定位信号灯信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }


        return null;
    }
    //    @RequiresPermissions("signal:adHOCPutConfig:putDeleteAdHOCLightInfo")

    @RequestMapping(value = "putDeleteAdHOCLightInfo")
    public AsyncResponseData.ResultData putDeleteAdHOCLightInfo(@RequestParam("collectorId")String  collectorId,@RequestParam("followLightAdress")String  followLightAdress,@RequestParam("masterLightId")String  masterLightId) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        byte[] followLightAdressBytes = NumberUtils.hexStringToByteArray(followLightAdress);

        ArrayList<Byte> followLightAdressBytesList = new ArrayList<>();
        for (byte  i=0; i<followLightAdressBytes.length; i++){
            followLightAdressBytesList.add(followLightAdressBytes[i]);
        }
        if (masterLightId.length() < 20) {
            masterLightId = String.format("%20s", masterLightId).replace(' ', '0');
        }

        byte[] masterLightIdBytes = NumberUtils.hexStringToByteArray(masterLightId);

        ArrayList<Byte> masterLightIdBytesBytesList = new ArrayList<>();
        for (byte  i=0; i<masterLightIdBytes.length; i++){
            masterLightIdBytesBytesList.add(masterLightIdBytes[i]);
        }


        TblCollector collector = tblCollectorService.get(collectorId);


        if (collector != null) {
            int count =Integer.parseInt( collector.getCounter());

            try {
                //下放
                daemonService.putDeleteAdHOCLightInfo(collector.getCode(),count, followLightAdressBytesList,masterLightIdBytesBytesList);//a6
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放删除灯信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }

            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,collectorId);


            messages.put("message", "下放删除灯信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }
        return null;
    }
    //    @RequiresPermissions("signal:adHOCPutConfig:putEndNetworkInfo")
    @RequestMapping(value = "putEndNetworkInfo")
    public AsyncResponseData.ResultData putEndNetworkInfo(@RequestParam("collectorId")String  collectorId) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        TblCollector collector = tblCollectorService.get(collectorId);
        if (collector != null) {
            int count =Integer.parseInt( collector.getCounter());
            tblAdHOCLightInfoTempService.deleteAdHOCEndScannerInfo(collectorId);

            String masterLightAdress = collector.getCode();
            if (masterLightAdress.length() < 20) {
                masterLightAdress = String.format("%20s", masterLightAdress).replace(' ', '0');
            }

            byte[] masterLightAdressBytes = NumberUtils.hexStringToByteArray(masterLightAdress);

            ArrayList<Byte> masterLightAdressBytesList = new ArrayList<>();
            for (byte  i=0; i<masterLightAdressBytes.length; i++){
                masterLightAdressBytesList.add(masterLightAdressBytes[i]);
            }
            try {
                //下放
                daemonService.putAdHOCEndNetworkInfo(collector.getCode(),count,masterLightAdressBytesList);//a7
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放结束组网信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "自组网下放方案成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,collectorId);
            messages.put("message", "下放结束组网信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }


        return null;
    }
    //    @RequiresPermissions("signal:adHOCPutConfig:putEndScannerInfo")

    @RequestMapping(value = "putEndScannerInfo")
    public AsyncResponseData.ResultData putEndScannerInfo(@RequestParam("collectorId")String  collectorId) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();

        if (collectorId==null) {
            messages.put("message", "参数信息不全");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_INTERNAL_ERROR);
            return data;
        }

        TblCollector collector = tblCollectorService.get(collectorId);

        if (collector != null) {

            int count =Integer.parseInt( collector.getCounter());

//        for (String lightId : lightIds) {


            String masterControllerId = collector.getCode();
            if ( masterControllerId.length() < 20) {
                masterControllerId = String.format("%20s", masterControllerId).replace(' ', '0');
            }

            byte[] masterLightIdBytes = NumberUtils.hexStringToByteArray(masterControllerId);

            ArrayList<Byte> masterLightIdBytesBytesList = new ArrayList<>();
            for (byte  i=0; i<masterLightIdBytes.length; i++){
                masterLightIdBytesBytesList.add(masterLightIdBytes[i]);
            }
            masterControllerId = masterControllerId.replaceFirst("^0+(?!$)", "");
            //删除本路口不是主控管理的信号灯
            tblAdHOCLightInfoTempService.deleteAdHOCEndScannerInfo(collectorId);
            for (Byte masterLightId : masterLightIdBytesBytesList) {
                System.out.println(masterLightId);
            }

            try {
                //下放
                daemonService.putAdHOCEndScannerInfo(collector.getCode(),count,masterLightIdBytesBytesList);//a8
                Thread.sleep(3000);
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<String> future = executor.submit(new Callable<String>() {
                    @Override
                    public String call() throws Exception {
                        String putFlag = tblCollectorService.getPutFlag(collector.getId());
                        if (putFlag != null) {
                            return putFlag;
                        }
                        return "0";
                    }
                });
                String putFlagResult = future.get();
                if (putFlagResult != null) {
                    if (!"1".equals(putFlagResult)) {
                        messages.put("message", "下放结束扫描信息失败");
                        data.setMessages(messages);
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
                        return data;
                    } /*else {
                        messages.put("message", "下放结束扫描信息成功") ;
                        data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                        data.setMessages(messages);
                        return data;

                    }*/
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }

            count++;
            if (count == 255) {
                count = 1;
            }
            //更新包序列
            tblCollectorService.updateCount(count,collectorId);
//        }
            messages.put("message", "下放结束扫描信息成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            return data;
        }
        return null;
    }
}