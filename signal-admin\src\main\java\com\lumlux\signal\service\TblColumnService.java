package com.lumlux.signal.service;

import com.heinqi.yangtes.jee.commons.persistence.Page;
import com.heinqi.yangtes.jee.commons.service.CrudService;
import com.heinqi.yangtes.jee.modules.sys.entity.User;
import com.heinqi.yangtes.jee.modules.sys.utils.UserUtils;
import com.lumlux.commons.dao.*;
import com.lumlux.commons.entity.*;
import com.lumlux.signal.condition.TblColumnCondition;
import com.lumlux.signal.condition.TblColumnHistoryCondition;
import com.lumlux.signal.condition.TblFailureCondition;
import com.lumlux.signal.util.WarrantyStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;



@Service
@Transactional(readOnly = true)
public class TblColumnService extends CrudService<TblColumnDao, TblColumn> {

    @Autowired
    private TblColumnDao columnDao;

    @Autowired
    private TblAlarmConfigService tblAlarmConfigService;


    @Autowired
    private UserExtService userExtService;

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean resetShaft(Map<String,Object> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);

        boolean b = false;
        boolean b1 = false;
        String id = param.get("id").toString();

        Map<String, Object> stringObjectMap = columnDao.byIdColumn(id);
        double attitudeXShaft = (double) stringObjectMap.get("attitude_x_shaft");
        double attitudeYShaft = (double) stringObjectMap.get("attitude_y_shaft");
        double attitudeZShaft = (double) stringObjectMap.get("attitude_z_shaft");

        b = columnDao.resetShaft(date,user,id,attitudeXShaft,attitudeYShaft,attitudeZShaft);
        b1 = columnDao.resetFailure(date, user, date, id);

        if (b == true && b1 == true) {
            return true;
        }
        return false;
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateDisplay(List<Map<String,Object>> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);

        boolean b = false;
        for (int i = 0; i < param.size(); i++) {

            String id = param.get(i).get("id").toString();
            String startTime = param.get(i).get("startTime").toString();
            String endTime = param.get(i).get("endTime").toString();
            String plan = param.get(i).get("plan").toString();
//            String remarks = param.get(i).get("remarks").toString();
            String remarks = "";

            b = columnDao.updateDisplay( startTime, endTime, plan,
                    user, date, remarks, id);
        }
        if (b == true) {
            return true;
        }
        return false;
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateAttitude(Map<String,Object> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);

        boolean b = false;

        String id = param.get("id").toString();
        String xShaft = param.get("xShaft").toString();
        String yShaft = param.get("yShaft").toString();
        String zShaft = param.get("zShaft").toString();


        b = columnDao.updateAttitude( xShaft, yShaft, zShaft,
                user, date, id);

        if (b == true) {
            return true;
        }
        return false;
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateColumnConfig(Map<String,Object> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);

        boolean b = false;

        String id = param.get("id").toString();
        String stateInterval = param.get("stateInterval").toString();
        String statusOfflineInterval = param.get("statusOfflineInterval").toString();
        String statusPartInvalidInterval = param.get("statusPartInvalidInterval").toString();


        b = columnDao.updateColumnConfig( stateInterval, statusOfflineInterval, statusPartInvalidInterval,
                user, date, id);

        if (b == true) {
            return true;
        }
        return false;
    }

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateHorn(List<Map<String,Object>> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);


        boolean b = false;
        for (int i = 0; i < param.size(); i++) {


            String id = param.get(i).get("id").toString();
            String startTime = param.get(i).get("startTime").toString();
            String endTime = param.get(i).get("endTime").toString();
            String plan = param.get(i).get("plan").toString();
//            String remarks = param.get(i).get("remarks").toString();

            String remarks = "";

            b = columnDao.updateHorn( startTime, endTime,
                    plan,  user, date, remarks, id);
        }
        if (b == true) {
            return true;
        }
        return false;

    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateProjector(List<Map<String,Object>> param) {


        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);


        boolean b = false;
        for (int i = 0; i < param.size(); i++) {


            String id = param.get(i).get("id").toString();
            String startTime = param.get(i).get("startTime").toString();
            String endTime = param.get(i).get("endTime").toString();
            String plan = param.get(i).get("plan").toString();
//            String remarks = param.get(i).get("remarks").toString();
            String remarks = "";
            String type = param.get(i).get("type").toString();

            b = columnDao.updateProjector(startTime, endTime, plan, user, date, remarks, id,type);
        }
        if (b == true) {
            return true;
        }
        return false;
    }
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean updateColumn(Map<String,Object> param){
        String user = UserUtils.getUser().getId();
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);


        String id = param.get("id").toString();
        String name = param.get("name").toString();
        String model = param.get("model").toString();
//        String enabled = param.get("enabled").toString();
//        String redProjectorEnabled = param.get("redProjectorEnabled").toString();
//        String greenProjectorEnabled = param.get("greenProjectorEnabled").toString();
//        String hornEnabled = param.get("hornEnabled").toString();
//        String displayEnabled = param.get("displayEnabled").toString();
        String iccid = param.get("iccid").toString();
        String code = param.get("code").toString();
        String longitude = param.get("longitude").toString();
        String latitude = param.get("latitude").toString();

        String address = param.get("address").toString();
        String deviceInstallTime = param.get("deviceInstallTime").toString();
        String supplierId = param.get("supplierId").toString();
        String officeId = param.get("officeId").toString();
        String supportId = param.get("supportId").toString();
        String areaId = param.get("areaId").toString();

        String prodDeliveryTime = param.get("prodDeliveryTime").toString();
        String prodEndTime = param.get("prodEndTime").toString();
        String remarks = param.get("remarks").toString();
//        String attitudeEnabled = param.get("attitudeEnabled").toString();
        String qnlineThreshold = param.get("qnlineThreshold").toString();
        /*String columnStatus = param.get("columnStatus").toString();
        */




        boolean b = columnDao.updateColumn(model,name, code, areaId, "1",  supplierId, supportId, officeId,  prodDeliveryTime,
                prodEndTime,  deviceInstallTime, iccid, "1", longitude, latitude,qnlineThreshold,
                user, date, address, remarks,"1","1","1","1", id);

        if(b==true){
            return true;
        }
        return false;
    }

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean addColumn(Map<String,Object> param) {
        Date dateString = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = dateFormat.format(dateString);

        String user = UserUtils.getUser().toString();
        String columnID = UUID.randomUUID().toString();

        String qnlineThreshold = param.get("qnlineThreshold").toString();
        String name = param.get("name").toString();
        String model = param.get("model").toString();

      /*  String enabled = param.get("enabled").toString();
        String redProjectorEnabled = param.get("redProjectorEnabled").toString();
            String greenProjectorEnabled = param.get("greenProjectorEnabled").toString();
        String hornEnabled = param.get("hornEnabled").toString();
        String displayEnabled = param.get("displayEnabled").toString();*/

        String iccid = param.get("iccid").toString();
        String code = param.get("code").toString();
        String longitude = param.get("longitude").toString();
        String latitude = param.get("latitude").toString();

        String deviceInstallTime = param.get("deviceInstallTime").toString();
        String supplierId = param.get("supplierId").toString();
        String officeId = param.get("officeId").toString();
        String supportId = param.get("supportId").toString();
        String areaId = param.get("areaId").toString();


        String prodDeliveryTime = param.get("prodDeliveryTime").toString();
        String prodEndTime = param.get("prodEndTime").toString();
        //String prodWarrantyStatus = param.get("prodWarrantyStatus").toString();

        String remarks = param.get("remarks").toString();
        String address = param.get("address").toString();
//        String attitudeEnabled = param.get("attitudeEnabled").toString();
        String redProjectorId = UUID.randomUUID().toString();
        String greenProjectorId = UUID.randomUUID().toString();
        String prodWarrantyStatus = String.valueOf(new WarrantyStatus().deviceWarrantyStatus(WarrantyStatus.stringtoDate(prodEndTime)));

        //添加控制器
        boolean b = columnDao.addColumn(columnID, name, code, model, areaId, "1", "1", supplierId, supportId, officeId, "0", date, prodDeliveryTime,
                prodEndTime, prodWarrantyStatus, deviceInstallTime, iccid, "1", "0.0", "0.0", "0.0", "0", "0", longitude, latitude,
                qnlineThreshold, user, date, user, date, address, remarks, "0",redProjectorId,"1","0","0",greenProjectorId,
                "1","0","0","1","0","1","0","0");



        /*boolean b = columnDao.addColumn(columnID, name, code, "13", areaId, enabled, "0", supplierId, supportId, officeId, "0", date, prodDeliveryTime,
                prodEndTime, prodWarrantyStatus, deviceInstallTime, iccid, attitudeEnabled, "0.0", "0.0", "0.0", "0", "0", longitude, latitude,
                "10", user, date, user, date, address, remarks, "0",redProjectorId,redProjectorEnabled,"0","0",greenProjectorId,
                greenProjectorEnabled,"0","0",hornEnabled,"0",displayEnabled,"0");*/


        boolean b1 = false;
        boolean b2 = false;
        boolean b3 = false;
        boolean b4 = false;
        boolean b5 = false;
        for (int i = 0; i < 3; i++) {
            String hornID = UUID.randomUUID().toString();
            String displayID = UUID.randomUUID().toString();
            // for (int j = 0; j < 2; j++) {
            /*String projectorID = UUID.randomUUID().toString();*/

            b1 = columnDao.addProjector(UUID.randomUUID().toString(), columnID,
                    String.valueOf(i),"18:00:00", "23:59:59", "0", user, date, user, date, "", "0",redProjectorId, "0");
            b4 = columnDao.addProjector(UUID.randomUUID().toString(), columnID,
                    String.valueOf(i),"18:00:00", "23:59:59", "0", user, date, user, date, "", "0",greenProjectorId, "1");
            //  }
            b2 = columnDao.addHorn(hornID, columnID,  String.valueOf(i), "09:00:00", "18:00:00",
                    "0", user, date, user, date, "", "0");

            b3 = columnDao.addDisplay(displayID, columnID, String.valueOf(i), "18:00:00", "23:59:59", "0",
                    user, date, user, date, "", "0");
        }

        // 初始化配置
        TblAlarmConfig tblAlarmConfig = new TblAlarmConfig("", columnID, "0", "0", "60", "10", "10", "10", "10", "0", "20", "0,0,00,00,00,00;1,0,00,00,00,00;2,0,00,00,00,00;", "0", "120", "0,0,00,00,00,00;1,0,00,00,00,00;2,0,00,00,00,00;", "0", "3", "0,0,00,00,00,00;1,0,00,00,00,00;2,0,00,00,00,00;", "0", "2", "0,0,00,00,00,00;1,0,00,00,00,00;2,0,00,00,00,00;","", UserUtils.getUser(), new Date(), UserUtils.getUser(), new Date(), "get", "0");
        tblAlarmConfig.setStatusOfflineInterval("300");//数据包间隔
        tblAlarmConfig.setStatusPartInvalidInterval("5");//部分失效时长
        tblAlarmConfig.setStatusOffInterval("5");//熄灯时长
        tblAlarmConfig.setStatusOverInterval("5");//过载时长
        tblAlarmConfig.setStatusFlickerInterval("5");//黄闪时长
        tblAlarmConfig.setStatusCircleFlickerInterval("10");//圆盘灯闪烁时长
        tblAlarmConfig.setStatusLackStrokeInterval("1");//倒计时断笔画判断周期
        tblAlarmConfig.setStatusLackStrokePrecision("0");//倒计时断笔画精度  0低 1高
        tblAlarmConfig.setStatusFailureInterval("5");//故障后数据自动上传间隔
        tblAlarmConfigService.save(tblAlarmConfig);

        if (b == true && b1 == true && b2 == true && b3 == true && b4 == true) {
            return true;
        }

        return false;
    }
    @Transactional(readOnly = false)
    public String listCode(String code){
        return dao.listCode(code);
    }
    public Page<TblColumn> selectVariablesByArea(TblColumnCondition condition){
        condition.setPage(condition.getPage().setList(dao.selectVariablesByArea(condition)));
        return condition.getPage();
    }

    public Page<TblFailure> getFailureHistorys(TblFailureCondition condition){
        condition.getPage().setList(dao.getFailureHistorys(condition));
        return condition.getPage();
    }

    public Page<TblColumnHistory> listColumnHistory(TblColumnHistoryCondition condition){
        condition.getPage().setList(dao.listColumnHistory(condition));
        return condition.getPage();
    }


    public List<Map<String, Object>> listProjector(Map<String,Object> param) {

        String columnId = param.get("id").toString();
        List<Map<String, Object>> projector = dao.listProjector(columnId);
        return projector;
    }
    public List<Map<String, Object>> listHorn(Map<String,Object> param) {

        String columnId = param.get("id").toString();
        List<Map<String, Object>> horn = dao.listHorn(columnId);
        return  horn;
    }

    public List<Map<String, Object>> listDisplay(Map<String,Object> param) {

        String columnId = param.get("id").toString();
        List<Map<String, Object>> display = dao.listDisplay(columnId);
        return display;
    }

    public List<Map<String, Object>> listColumnConfig(Map<String,Object> param) {

        String columnId = param.get("id").toString();
        List<Map<String, Object>> columnConfig = dao.listColumnConfig(columnId);
        return columnConfig;
    }

    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public boolean deleteColumn(Map<String,Object> param) {
        String id = param.get("id").toString();
        boolean deleteDisplay = dao.deleteDisplay(id);
        boolean deleteHorn = dao.deleteHorn(id);
        boolean deleteProjector = dao.deleteProjector(id);
        boolean deleteColumn = dao.deleteColumn(id);

        if (deleteDisplay == true && deleteHorn == true && deleteProjector == true && deleteColumn == true) {
            return true;
        }
        return false;
    }
    @Transactional(readOnly = false)
    public void enableColumn(String enabled, String id,String model) {
        dao.enableColumn(enabled, id,model);
    }
    @Transactional(readOnly = false)
    public void enableRedProjector(String redProjectorEnabled, String id,String model) {
        dao.enableRedProjector(redProjectorEnabled, id,model);
    }
    @Transactional(readOnly = false)
    public void enableGreenProjector(String greenProjectorEnabled, String id,String model) {
        dao.enableGreenProjector(greenProjectorEnabled, id,model);
    }
    @Transactional(readOnly = false)
    public void enableHorn(String hornEnabled, String id,String model) {
        dao.enableHorn(hornEnabled, id,model);
    }
    @Transactional(readOnly = false)
    public void enableDisplay(String displayEnabled, String id,String model) {
        dao.enableDisplay(displayEnabled, id,model);
    }
    @Transactional(readOnly = false)
    public void enableAttitude(String attitudeEnabled, String id,String model) {
        dao.enableAttitude(attitudeEnabled, id,model);
    }


    public List<TblProjector> listProjectorRed(String id, String type) {
        List<TblProjector> projector = dao.listProjectorRed(id,type);
        return projector;
    }
    public List<TblProjector> listProjectorGreen(String id, String type) {
        List<TblProjector> projector = dao.listProjectorGreen(id,type);
        return projector;
    }
    public List<TblHorn> listHorns(String id) {
        List<TblHorn> tblHorns = dao.listHorns(id);
        return tblHorns;
    }

    public List<TblDisplay> listDisplays(String id) {
        List<TblDisplay> tblDisplays = dao.listDisplays(id);
        return tblDisplays;

    }

    public TblColumn getThresholdXYZ(String id) {
        return dao.getThresholdXYZ(id);
    }

    public List<Map<String, Object>> listFailureHistorys(Map<String,Object> param) {
        User currentUser = UserUtils.getUser();
        UserExt userExt = userExtService.get(currentUser.getId());
        String supportId = userExt.getSupportId();

        String description = param.get("description").toString();
        String columnName = param.get("columnName").toString();
        String startDate = param.get("startDate").toString();
        String endDate = param.get("endDate").toString();
        List<Map<String, Object>> projector = dao.listFailureHistorys(supportId,description,columnName,startDate,endDate);
        return projector;
    }

    public List<ColumnSupplier> getSupplyAnaly() {

        return dao.getSupplyAnaly();
    }

    public int getColumnSupplyStatus(Integer model) {
        return dao.columnSupplyStatus(model);
    }

    public int getColumnSupplyStatusInWar(Integer model) {
        return dao.getColumnSupplyStatusInWar(model);
    }

    public Integer getColumnSupplyStatusOutWar(Integer model) {
        return dao.getColumnSupplyStatusOutWar(model);
    }

    public Integer getColumnSupplyStatusFailure(Integer model) {
        return dao.getColumnSupplyStatusFailure(model);
    }

    public Integer getColumnSupplyStatusOnline(Integer model) {
        return dao.getColumnSupplyStatusOnline(model);
    }

    public Integer getControllerSum() {
        return dao.getControllerSum();
    }

    public Integer getColumnSupplyStatusShaft(Integer model) {
        return dao.getColumnSupplyStatusShaft(model);
    }

    public List<String> findCode() {
        return dao.findCode();
    }

    public Integer getCrossroadSum() {
        return dao.getCrossroadSum();
    }

    public Integer getProSum() {
        return dao.getProSum();
    }

    public List<Map<String, Object>> getColInfo() {
        return dao.getColInfo();
    }
}