package com.lumlux.signal.service;

import com.heinqi.yangtes.jee.commons.persistence.Page;
import com.heinqi.yangtes.jee.commons.service.CrudService;
import com.lumlux.commons.dao.TblColumnFailureDao;
import com.lumlux.commons.entity.TblFailure;
import com.lumlux.signal.condition.TblFailureCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TblColumnFailureService extends CrudService<TblColumnFailureDao, TblFailure> {

	@Autowired
	private TblColumnFailureDao tblColumnFailureDao;

	public TblFailure get(final String id) {
		return super.get(id);
	}

	public List<TblFailure> findList(final TblFailure tblFailure) {
		return super.findList(tblFailure);
	}
	
	public Page<TblFailure> findList(TblFailureCondition condition){
		condition.setPage(super.findListByCondtion(condition));
		return condition.getPage();
	}
	
	@Transactional(readOnly = false)
	public void save(final TblFailure tblFailure) {
		super.save(tblFailure);
	}

	@Transactional(readOnly = false)
	public void delete(final TblFailure tblFailure) {
		super.delete(tblFailure);
	}

	@Transactional(readOnly = false)
	public void deleteBatch(List<String> ids) {
		dao.deleteBatch(ids);
	}

	@Transactional(readOnly = false)
	public void updateStatus(String status,String id){
		dao.updateStatus(status,id);
	}
	
	public Page<TblFailure> getFailureHistory(TblFailureCondition condition){
		condition.getPage().setList(tblColumnFailureDao.getFailureHistory(condition));
		return condition.getPage();
	}

	public Page<TblFailure> getFailureHistorys(TblFailureCondition condition){
		condition.getPage().setList(tblColumnFailureDao.getFailureHistorys(condition));
		return condition.getPage();
	}
	
	public List<TblFailure> getFailureHistoryList(TblFailureCondition condition){
		return tblColumnFailureDao.getFailureHistory(condition);
	}
	
	@Transactional(readOnly = false)
	public void deleteFailure(String collectorId) {
		dao.deleteFailure(collectorId);
	}

    public List<TblFailure> findAllFailure(String collectorId) {
		return tblColumnFailureDao.findAllFailure(collectorId);
    }

	public void insertFailure(List<TblFailure> failures) {
		tblColumnFailureDao.insertFailure(failures);
	}

	@Transactional(readOnly = false)
	public void insert(TblFailure tbfailure) {
		tblColumnFailureDao.insert(tbfailure);
	}


//	@Transactional(readOnly = false)
//	public boolean updateReportType(Map<String, Object> param){
//		String id = param.get("id").toString();
//		Integer reportType = Integer.valueOf(param.get("reportType").toString());
//		return tblColumnFailureDetailDao.updateReportType(id,reportType);
//	}
}