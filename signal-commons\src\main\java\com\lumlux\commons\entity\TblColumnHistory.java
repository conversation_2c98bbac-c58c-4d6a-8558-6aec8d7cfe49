package com.lumlux.commons.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heinqi.yangtes.jee.commons.persistence.DataEntity;
import com.heinqi.yangtes.jee.modules.sys.entity.Area;
import com.heinqi.yangtes.jee.modules.sys.entity.Office;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

public class TblColumnHistory extends DataEntity<TblColumnHistory> {


	private static final long serialVersionUID = 1L;

	private String id;
	private String columnId;
	private String redProjectorPower;
	private String redProjectorStatus;
	private String greenProjectorPower;
	private String greenProjectorStatus;
	private String columnPower;
	private String columnStatus;
	private String hornStatus;
	private String displayStatus;
	private String longitude;
	private String latitude;

	private double attitudeXshaft;
	private double attitudeYshaft;
	private double attitudeZshaft;
	private String remarks;
	private String delFlag;
	private String areaId;

	private String name;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAreaId() {
		return areaId;
	}

	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}

	@Override
	public String getId() {
		return id;
	}

	@Override
	public void setId(String id) {
		this.id = id;
	}

	public String getColumnId() {
		return columnId;
	}

	public void setColumnId(String columnId) {
		this.columnId = columnId;
	}

	public String getRedProjectorPower() {
		return redProjectorPower;
	}

	public void setRedProjectorPower(String redProjectorPower) {
		this.redProjectorPower = redProjectorPower;
	}

	public String getRedProjectorStatus() {
		return redProjectorStatus;
	}

	public void setRedProjectorStatus(String redProjectorStatus) {
		this.redProjectorStatus = redProjectorStatus;
	}

	public String getGreenProjectorPower() {
		return greenProjectorPower;
	}

	public void setGreenProjectorPower(String greenProjectorPower) {
		this.greenProjectorPower = greenProjectorPower;
	}

	public String getGreenProjectorStatus() {
		return greenProjectorStatus;
	}

	public void setGreenProjectorStatus(String greenProjectorStatus) {
		this.greenProjectorStatus = greenProjectorStatus;
	}

	public String getColumnPower() {
		return columnPower;
	}

	public void setColumnPower(String columnPower) {
		this.columnPower = columnPower;
	}

	public String getColumnStatus() {
		return columnStatus;
	}

	public void setColumnStatus(String columnStatus) {
		this.columnStatus = columnStatus;
	}

	public String getHornStatus() {
		return hornStatus;
	}

	public void setHornStatus(String hornStatus) {
		this.hornStatus = hornStatus;
	}

	public String getDisplayStatus() {
		return displayStatus;
	}

	public void setDisplayStatus(String displayStatus) {
		this.displayStatus = displayStatus;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public double getAttitudeXshaft() {
		return attitudeXshaft;
	}

	public void setAttitudeXshaft(double attitudeXshaft) {
		this.attitudeXshaft = attitudeXshaft;
	}

	public double getAttitudeYshaft() {
		return attitudeYshaft;
	}

	public void setAttitudeYshaft(double attitudeYshaft) {
		this.attitudeYshaft = attitudeYshaft;
	}

	public double getAttitudeZshaft() {
		return attitudeZshaft;
	}

	public void setAttitudeZshaft(double attitudeZshaft) {
		this.attitudeZshaft = attitudeZshaft;
	}


	@Override
	public String getRemarks() {
		return remarks;
	}

	@Override
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	@Override
	public String getDelFlag() {
		return delFlag;
	}

	@Override
	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}

	private Date createDate;

	private Date updateDate;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Override
	public Date getCreateDate() {
		return createDate;
	}

	@Override
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Override
	public Date getUpdateDate() {
		return updateDate;
	}

	@Override
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	@Override
	public String toString() {
		return "TblColumnHistory{" +
				"id='" + id + '\'' +
				", columnId='" + columnId + '\'' +
				", redProjectorPower='" + redProjectorPower + '\'' +
				", redProjectorStatus='" + redProjectorStatus + '\'' +
				", greenProjectorPower='" + greenProjectorPower + '\'' +
				", greenProjectorStatus='" + greenProjectorStatus + '\'' +
				", columnPower='" + columnPower + '\'' +
				", columnStatus='" + columnStatus + '\'' +
				", hornStatus='" + hornStatus + '\'' +
				", displayStatus='" + displayStatus + '\'' +
				", longitude='" + longitude + '\'' +
				", latitude='" + latitude + '\'' +
				", attitudeXshaft=" + attitudeXshaft +
				", attitudeYshaft=" + attitudeYshaft +
				", attitudeZshaft=" + attitudeZshaft +
				", remarks='" + remarks + '\'' +
				", delFlag='" + delFlag + '\'' +
				", createDate=" + createDate +
				", updateDate=" + updateDate +
				'}';
	}
}