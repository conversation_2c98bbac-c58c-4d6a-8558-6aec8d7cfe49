<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>
<%@ taglib prefix="ui" tagdir="/WEB-INF/tags/ui" %>


<layout:default title="故障表管理">
    <link rel="stylesheet" href="${ctxStatic}/bootstrap/css/bootstrap-3.3.7.css">
    <link rel="stylesheet" href="${ctxStatic}/bootstrap/css/bootstrap-daterangepicker-2.1.25.css">
    <link rel="stylesheet" href="${ctxStatic}/bootstrap/css/bootstrap-select-1.10.0.css">
    <script src="${ctxStatic}/bootstrap/js/bootstrap-3.3.7.js"></script>
    <script src="${ctxStatic}/bootstrap/js/bootstrap-select-1.10.0.js"></script>
    <script src="${ctxStatic}/bootstrap/js/bootstrap-daterangepicker-2.1.25-moment.js"></script>
    <script src="${ctxStatic}/bootstrap/js/bootstrap-daterangepicker-2.1.25.js"></script>
    <style>
        .a1{
            color:#333;
        }
        .a2{
            color:red;
        }
        .nav>li>a{
            position: relative;
            display: block;
            padding:8px 12px 8px 12px;
            font-size:13px;
        }
        .nav-tabs>li>a {
            padding-top: 8px;
            padding-bottom: 8px;
            line-height: 20px;
            border: 1px solid transparent;
        }
        .daterangepicker select.hourselect,	.daterangepicker select.minuteselect{
            width: 55px;
            margin-bottom: 0;
        }
        .select2-container-multi .select2-choices {
            min-height: 28px;
        }
        .controls >a {
            -webkit-user-drag: none;
        }
        .controls >a >input{
            border:1px solid !important;
            margin: 0 2px 5px 5px !important;
        }
        .controls >a >input:last-child {
            border:1px solid !important;
            margin: 0 5px 5px 2px !important;
        }
        .table td {
            line-height: 26px !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function() {
            $("#searchForm").validate({
                submitHandler: function(form){
                    loading('正在查询，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });
        function page(n,s){
            $("#pageNo").val(n);
            $("#pageSize").val(s);
            $("#model").val();
            $("#searchForm").submit();
            return false;
        }

        var doPost = function(url,jsonDate,callback){
            $.ajax({type: "POST",url: url,data: JSON.stringify(jsonDate),
                contentType: "application/json; charset=utf-8",dataType: "json",
                success: callback,
                error: function (msg) {
                    console.log(msg);
                }
            });
        }

       /* function init(dtStart,dtEnd) {
            //定义locale汉化插件
            var locale = {
                "format": 'YYYY-MM-DD HH:mm:ss',
                "separator": " -222 ",
                "applyLabel": "确定",
                "cancelLabel": "取消",
                "fromLabel": "起始时间",
                "toLabel": "结束时间'",
                "customRangeLabel": "自定义",
                "weekLabel": "W",
                "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
                "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
                "firstDay": 1
            };
            //初始化显示当前时间
            if(dtStart == "" || dtEnd == "")
            {
                $('#daterange-btn span').html(moment().format('YYYY-MM-DD HH:mm:ss') + ' - ' + moment().format('YYYY-MM-DD HH:mm:ss'));
                dtStart = moment().format('YYYY-MM-DD HH:mm:ss');
                dtEnd = moment().format('YYYY-MM-DD HH:mm:ss');
                $("#startDate").val(dtStart);
                $("#endDate").val(dtEnd);
            }else{
                $('#daterange-btn span').html(dtStart + ' - ' + dtEnd);
            }

            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'opens': 'left',
                    'locale': locale,
                    //showDropdowns: true,
                    //showWeekNumbers: true,
                    timePicker: true,
                    timePickerIncrement: 1,
                    timePicker24Hour: true,
                    startDate: dtStart,
                    endDate: dtEnd,
                    //汉化按钮部分
                    ranges: {
                        '今日': [moment().format('YYYY-MM-DD') + " 00:00:00", moment().format('YYYY-MM-DD') + " 23:59:59"],
                        '昨日': [moment().subtract(1, 'days').format('YYYY-MM-DD') + " 00:00:00", moment().subtract(1, 'days').format('YYYY-MM-DD') + " 23:59:59"],
                        '最近7日': [moment().subtract(6, 'days').format('YYYY-MM-DD') + " 00:00:00", moment().format('YYYY-MM-DD') + " 23:59:59"],
                        '最近30日': [moment().subtract(29, 'days').format('YYYY-MM-DD') + " 00:00:00", moment().format('YYYY-MM-DD') + " 23:59:59"],
                        '本月': [moment().startOf('month').format('YYYY-MM-DD') + " 00:00:00", moment().endOf('month').format('YYYY-MM-DD') + " 23:59:59"],
                        '上月': [moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD') + " 00:00:00", moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD') + " 23:59:59"]
                    },

                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD HH:mm:ss') + ' - ' + end.format('YYYY-MM-DD HH:mm:ss'));
                    $("#startDate").val(start.format('YYYY-MM-DD HH:mm:ss'));
                    $("#endDate").val(end.format('YYYY-MM-DD HH:mm:ss'));
                }
            );
        };*/
      /*  $().ready(function() {
            init($("#startDate").val(),$("#endDate").val());
        });*/

       /* function collectorChange(){
            var collectorId = $("#collectorSelect").val();
            $("#collectorId").val(collectorId);
            if(collectorId !=null){
                if(collectorId.length==1){
                    //1
                    $("#circuitSelect").html("");
                    var param = {"id": collectorId[0]};
                    $.ajax({
                        url:"${ctx}/signal/tblDataHistory/getCircuitSelect",
                        type:"post",
                        dataType: "json",
                        data: JSON.stringify(param),
                        contentType: "application/json; charset=utf-8",dataType: "json",
                        success:function(result){
                            if(result.data!=null){
                                for(var m in result.data){
                                    $("#circuitSelect").append('<option value="'+result.data[m].lightAddress+'">'+ result.data[m].lightAddress +'</option>');
                                }
                            }
                        }
                    });

                }else{//2+
                    $("#circuitSelect").html("");
                }
            }else{
                $("#circuitSelect").html("");
            }
        }*/

        function circuitChange(){
            $("#circuitAddress").val($("#circuitSelect").val());
        }

        function statusChange(){
            $("#statusValue").val($("#statusSelect").val());
        }

        function updateReportType(collectorId, reportType) {
            doPost('${ctx}/signal/echarts/updateReportType', {id: collectorId, reportType: reportType}, res => top.$.jBox.tip("评价" + (res ? "成功" : "失败"), res ? "success" : "error"))
        }
    </script>
    <ul class="nav nav-tabs">
        <li><a href="${ctx}/signal/tblCollector/listColumn?condition.model=14 &condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">标志牌列表</a></li>
    <li class="active"><a href="${ctx}/signal/tblAlarmConfig/listFailureHistory?condition.collectorId=${form.condition.collectorId}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.startDate=&condition.endDate=">故障查询</a>
    </ul>


    <form:form id="searchForm" modelAttribute="form" action="${ctx}/signal/tblAlarmConfig/listFailureHistory?condition.model=14&condition.startDate=&condition.endDate=" method="post" >


        <input id="pageNo" name="condition.page.pageNo" type="hidden" value="${form.condition.page.pageNo}"/>
        <input id="pageSize" name="condition.page.pageSize" type="hidden" value="${form.condition.page.pageSize}"/>
        <input id="dataEntityId" name="dataEntity.id" type="hidden" value=""/>
        <input id="collectorId" name="condition.collectorId" type="hidden" value="${form.condition.collectorId}"/>
        <input id="model" name="dataEntity.model" type="hidden" value="14"/>
      <%--  <input id="startDate" name="condition.startDate" type="hidden" value=""/>
        <input id="endDate" name="condition.endDate" type="hidden" value="${form.condition.endDate}"/>--%>

    </form:form>
    <sys:message content="${message}" />
    <p id="message" class="alert alert-success" style="display:none"></p>
    <table id="contentTable" class="table table-striped table-bordered table-condensed" style="font-size:13px;">
        <thead>
        <tr>
            <th>主机名称</th>
            <th>主机地址</th>
            <th>故障状态</th>
            <th>处理状态</th>
            <th>发生时间</th>
            <th>恢复时间</th>
            <th>更新时间</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${form.condition.page.list}" var="tblFailure">
            <tr>
                <td>${tblFailure.name}</td>
                <td>${tblFailure.address}</td>
                <td>${tblFailure.description}</td>
                <c:choose>
                    <c:when test="${tblFailure.status eq 0}">
                        <td value="${tblFailure.status}">发生</td>
                    </c:when>
                    <c:when test="${tblFailure.status eq 1}">
                        <td value="${tblFailure.status}">处理中</td>
                    </c:when>
                    <c:otherwise>
                        <td value="${tblFailure.status}">已处理</td>
                    </c:otherwise>
                </c:choose>
                <td><fmt:formatDate value="${tblFailure.createDate}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                <td><c:if test="${tblFailure.status eq 2}"><fmt:formatDate value="${tblFailure.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/></c:if></td>
                <td><fmt:formatDate value="${tblFailure.updateDate}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
    <div class="pagination">${form.condition.page}</div>
</layout:default>