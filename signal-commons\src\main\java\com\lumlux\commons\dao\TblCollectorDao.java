package com.lumlux.commons.dao;

import com.heinqi.yangtes.jee.base.AbstractCondition;
import com.heinqi.yangtes.jee.commons.persistence.CrudDao;
import com.heinqi.yangtes.jee.commons.persistence.annotation.MyBatisDao;
import com.heinqi.yangtes.jee.modules.sys.entity.Area;
import com.lumlux.commons.entity.CollectorSupplier;
import com.lumlux.commons.entity.TblCollector;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

@MyBatisDao
public interface TblCollectorDao extends CrudDao<TblCollector> {

    public List<Area> selectAllCities();

    public List<Area> selectAreasByParentId(String id);

    public List<TblCollector> selectCollectorTreeData(String loginName);

    public TblCollector selectCollectorById(String id);
    
    public TblCollector selectCollectorAppById(String id);

    public List<TblCollector> selectCollectorsByArea(AbstractCondition<TblCollector> condition);

    public List<TblCollector> selectPillarsByArea(AbstractCondition<TblCollector> condition);
    
//    public List<TblCollector> selectCollectorsByArea1(@Param("officeId")String officeId,@Param("officeId1")String officeId1,@Param("supportId")String supportId);
    
    public List<TblCollector> selectVariableCollectorsByArea(AbstractCondition<TblCollector> condition);

    public void enableCollector(String enabled,String id);

    public void simNoCollector(String simNo,String id);

    public void failureStatusCollector(String failureStatus,String id);

    public Integer selectCollectorByCode(String code);

    public TblCollector getCollectorByCode(@Param("code")String code);
    
    public Integer selectCollectorByName(String name);
    

	public List<TblCollector> findAppList(AbstractCondition<TblCollector> condition);

    Long insertBatch(@Param("list") List<TblCollector> list);

    public List<TblCollector> findListbyOfficeId(@Param("id")String id,@Param("parentid")String parentid);
    
    public TblCollector getPicture(String id);
    
    public TblCollector findVariableForm(String id);
    
    public List<TblCollector> findConsole(final TblCollector tblCollector);
    
    public void deleteFailure(@Param("collectorId")String collectorId);

    public List<CollectorSupplier> findAllSupplyName();

    public Integer collectorSum();

    public Integer signalFailueSum();


    public  Integer sightLightSum();

    public Integer failureCollectorSum();

    double collectTotalSum();

    double collectRepairedSum();

    List<Map<String, Object>>  failureCollectorB(@Param(value = "startDate")String startDate);

    int failureCollectorC(@Param(value = "startDate")String startDate);

    List<Map<String, Object>> listCollector();

    int cityService();

    List<Map<String, Object>> listCollectorFailure();

    List<Map<String, Object>> listCollectorWarranty();

    List<Map<String, Object>> listCollectorWarrantySum();


    Integer  updCollectorWarrantyStatus(@Param("id")String id,@Param("CollectorWarrantyStatus")Integer CollectorWarrantyStatus);


    List<Map<String, Object>> listCircuit();

    Integer updCircuitWarrantyStatus(@Param("id")String id,@Param("circuitWarrantyStatus")Integer circuitWarrantyStatus);


    public void updateVersion1(@Param("version")String version,@Param("collectorId")String collectorId);

    public TblCollector checkedIccid(@Param("iccid")String iccid);


    boolean updCollectorQnlineThreshold(@Param("qnlineThreshold")String qnlineThreshold,@Param("id")String id);

    Integer sgSum();
    boolean updChannel(@Param("id")String id,@Param("enabled")Integer enabled );

    boolean updateFailure(@Param("id")String id,@Param("goal")Integer goal);

    boolean updateVariable(@Param("id")String id,@Param("name")String name,@Param("code")String code,@Param("imei")String imei,@Param("areaId")String areaId,@Param("boardQuantity")Integer boardQuantity,
                           @Param("startTime")String startTime,@Param("endTime")String endTime,@Param("dimmingControlStatus")String dimmingControlStatus,@Param("longitude")String  longitude,@Param("latitude")String  latitude,
                           @Param("pictureUrl")String  pictureUrl, @Param("enabled")String enabled,@Param("supplierId")String supplierId,@Param("supportId")String supportId,@Param("officeId")String officeId,
                           @Param("simNo")String simNo,@Param("updateBy")String  updateBy,@Param("updateDate")String  updateDate,
                           @Param("address")String  address,@Param("remarks")String  remarks,@Param("model")String  model,@Param("isInform")String  isInform,@Param("ledCount")String  ledCount,
                           @Param("deliveryTime")String  deliveryTime,@Param("prodEndTime")String prodEndTime,@Param("deviceInstallTime")String  deviceInstallTime,@Param("iccid")String  iccid,
                           @Param("qnlineThreshold")String  qnlineThreshold);


    boolean addVariable(@Param("id")String id,@Param("name")String name,@Param("model")String model,@Param("code")String code,@Param("imei")String imei,
                        @Param("areaId")String areaId,@Param("boardQuantity")Integer boardQuantity,@Param("dimmingStartTime")String dimmingStartTime,
                        @Param("dimmingEndTime")String dimmingEndTime,@Param("dimmingControlStatus")String dimmingControlStatus,@Param("longitude")String longitude,
                        @Param("latitude")String latitude,@Param("pictureUrl")String pictureUrl,@Param("enabled")String enabled,@Param("status")String status,@Param("failureStatus")String failureStatus,
                        @Param("supportId")String supportId,
                        @Param("supplierId")String supplierId,@Param("officeId")String officeId,@Param("simNo")String simNo, @Param("createBy")String createBy,
                        @Param("createDate")String createDate, @Param("updateBy")String updateBy, @Param("updateDate")String updateDate, @Param("address")String address,
                        @Param("remarks")String remarks,@Param("prodDeliveryTime")String prodDeliveryTime,@Param("prodEndTime")String prodEndTime,@Param("prodWarrantyStatus")Integer prodWarrantyStatus,
                        @Param("deviceInstallTime")String deviceInstallTime,@Param("iccid")String iccid,@Param("qnlineThreshold")String qnlineThreshold,@Param("delFlag")String delFlag,@Param("type")String type);

    boolean addConfig(@Param("id")String id,@Param("variableId")String variableId,@Param("enabled")String enabled,@Param("createBy")String createBy,@Param("createDate")String createDate,@Param("updateBy")String updateBy,@Param("updateDate")String updateDate,@Param("delFlag")String delFlag);
    boolean addChannel(@Param("id")String id,@Param("variableSchemeId")String variableSchemeId,@Param("enabled")String enabled,@Param("createBy")String createBy,@Param("createDate")String  createDate,@Param("updateBy")String updateBy,@Param("updateDate")String updateDate,@Param("delFlag")String delFlag,@Param("channelPosition")Integer channelPosition,@Param("pattern")Integer pattern);

    boolean addfangan(@Param("id")String id,@Param("variableId")String variableId,@Param("enabled")String enabled,@Param("schemeStartTime")String schemeStartTime,@Param("schemeEndTime")String schemeEndTime,@Param("createBy")String createBy,@Param("createDate")String  createDate,@Param("updateBy")String updateBy,@Param("updateDate")String updateDate,@Param("delFlag")String delFlag,@Param("type")String type,@Param("controlType")String controlType);

    List<Map<String, Object>> listChannel(@Param("id") String id);

    List<Map<String, Object>>  listConfig(@Param("id") String id);



    List<Map<String, Object>>  list(@Param("id") String id,@Param("type") String type);
    List<Map<String, Object>>  listDisplay(@Param("id") String id,@Param("type") String type);

    String  listCode(@Param("code") String code);

    List<Map<String, Object>>  variableById(@Param("id") String id);


//    TblCollector getAllCollector(@Param("code")String code,@Param("goal") int goal);


    Integer boardQuantityById(@Param("id") String id);

    boolean delChannelAndPlan(@Param("id")String id);

    boolean  updOnlineMinutes(@Param("onlineMinutes")Integer onlineMinutes,@Param("status")String status,@Param("id")String id);
    boolean  updfailure(@Param("failure")String failure,@Param("id")String id);

    List<Map<String, Object>>  onlineMinutesById(@Param("id") String id);

    boolean updateEnabled(@Param("id")String id,@Param("enabled")String enabled);


    boolean deleteVariable(@Param("id")String id);

    int selectCount(@Param("id")String collectorId);

    void updateCount(@Param("count")int count,@Param("id")String collectorId);

    String getPutFlag(@Param("id")String id);


    List<Map<String, Object>>  allFailureCollector(@Param("supportId") String supportId);

    void updateDimmingParam(@Param("endTimeDate")String endTimeDate,@Param("startTimeDate") String startTimeDate,@Param("controlStatus") String controlStatus,@Param("id") String id);

    void updateCountAndVersion(Integer count, String id, int i);

    void updatePutFlag(@Param("id")String code,@Param("flag") int i);



    boolean updateTpCollector(@Param("name")String name, @Param("imei")String imei, @Param("iccid")String iccid,
                              @Param("areaId")String areaId,@Param("address")String address,
                              @Param("qnlineThreshold") String qnlineThreshold, @Param("id")String id);


    @Update(" UPDATE tp_collector SET del_flag ='1'  WHERE id=#{id} and del_flag ='0'")
    boolean  deleteTpCollector(@Param("id") String id);

    boolean addTpCollector(@Param("id")String id,@Param("name")String name, @Param("imei")String imei, @Param("iccid")String iccid,
                              @Param("areaId")String areaId,@Param("status")String status, @Param("address")String address,
                              @Param("qnlineThreshold") String qnlineThreshold,@Param("type") String type,
    @Param("ip") String ip, @Param("port") String port , @Param("lampStatus") String lampStatus, @Param("brushStrokeStatus") String brushStrokeStatus,
    @Param("attitudeStatus") String attitudeStatus ,@Param("longitude") String longitude,@Param("latitude") String latitude ,@Param("createDate") String createDate,@Param("updateDate") String updateDate ,@Param("delFlag") String delFlag);

    String  listImei(@Param("imei") String imei);


    List<Map<String, Object>>  listTpCollector(@Param("name") String name,@Param("areaId") String areaId,@Param("pageNo") Integer pageNo,@Param("pageSize") Integer pageSize);

    Integer  listTpCollectorCount();

    List<Map<String, Object>>  byIdTpCollectorHistory(@Param("collectorId") String collectorId);


}