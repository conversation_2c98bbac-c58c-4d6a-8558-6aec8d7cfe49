package com.lumlux.commons.entity;

/**
 * <p>Title: ColumnSupplyStatus.java</p>
 * <p>Description: ColumnSupplyStatus单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2025/2/7</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class ColumnSupplyStatus {
    private Integer collumnSum;

    private Integer  InWarrantyPeriodSum;

    private Integer OutWarrantyPeriodSum;

    private Integer  onlineSum;

    private Integer failureSum;

    private Integer shaftSum;

    public Integer getShaftSum() {
        return shaftSum;
    }

    public void setShaftSum(Integer shaftSum) {
        this.shaftSum = shaftSum;
    }

    public Integer getCollumnSum() {
        return collumnSum;
    }

    public void setCollumnSum(Integer collumnSum) {
        this.collumnSum = collumnSum;
    }

    public Integer getInWarrantyPeriodSum() {
        return InWarrantyPeriodSum;
    }

    public void setInWarrantyPeriodSum(Integer inWarrantyPeriodSum) {
        InWarrantyPeriodSum = inWarrantyPeriodSum;
    }

    public Integer getOutWarrantyPeriodSum() {
        return OutWarrantyPeriodSum;
    }

    public void setOutWarrantyPeriodSum(Integer outWarrantyPeriodSum) {
        OutWarrantyPeriodSum = outWarrantyPeriodSum;
    }

    public Integer getOnlineSum() {
        return onlineSum;
    }

    public void setOnlineSum(Integer onlineSum) {
        this.onlineSum = onlineSum;
    }

    public Integer getFailureSum() {
        return failureSum;
    }

    public void setFailureSum(Integer failureSum) {
        this.failureSum = failureSum;
    }
}
