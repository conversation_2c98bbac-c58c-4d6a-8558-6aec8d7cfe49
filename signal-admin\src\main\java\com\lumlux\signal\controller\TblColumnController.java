package com.lumlux.signal.controller;

import com.alibaba.fastjson.JSONObject;
import com.heinqi.yangtes.base.web.AsyncResponseData;
import com.heinqi.yangtes.jee.commons.web.BaseController;
import com.heinqi.yangtes.jee.modules.sys.entity.User;
import com.heinqi.yangtes.jee.modules.sys.utils.UserUtils;
import com.heinqi.yangtes.jee.commons.utils.StringUtils;
import com.lumlux.commons.entity.TblCollector;
import com.lumlux.commons.entity.TblColumn;
import com.lumlux.commons.entity.UserExt;
import com.lumlux.signal.condition.TblColumnCondition;
import com.lumlux.signal.condition.TblFailureCondition;
import com.lumlux.signal.entity.ShiftEntity;

import com.lumlux.signal.form.TblFailureForm;
import com.lumlux.signal.service.*;
import com.lumlux.signal.util.Constant;
import com.lumlux.signal.util.ResponseUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "${adminPath}/signal/tblColumn")
public class TblColumnController extends BaseController
{

    /**
     * @serialField
     */

    @Autowired
    private TblColumnFailureService tblColumnFailureService;

    @Autowired
    private UserExtService userExtService;

    @Autowired
    private DaemonService daemonService;

    @Autowired
    private TblColumnService tblColumnService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData columnList(@RequestBody TblColumn tblColumn) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        TblColumnCondition condition = new TblColumnCondition();
        if(tblColumn.getAreaId() !=null)
        {
            condition.setAreaId(tblColumn.getAreaId());
        }
        condition.setModel(tblColumn.getModel());
        condition.setName(tblColumn.getName());
        // 根据登录用户身份筛选集中器;总队（全部），支队（集中器的管理单位），维修人员（集中器的维修单位）
        User user = UserUtils.getUser();
        UserExt userExt = userExtService.get(user.getId());
        if (user.getUserType().equals(Constant.USER_TYPE.DIVISION)) { // 总队
            condition.setDevisionId(user.getOffice().getId());
        }
        if (user.getUserType().equals(Constant.USER_TYPE.DETACHMENT)) { // 支队
            condition.setDetachmentId(user.getOffice().getId());
        }
        if (user.getUserType().equals(Constant.USER_TYPE.SUPPORT) || user.getUserType().equals(Constant.USER_TYPE.SUPPORTOR)) { // 维修
            condition.setSupportId(userExt.getSupportId());
        }
        condition.getPage().setPageNo(1);
        condition.getPage().setPageSize(1000);

        List<TblColumn> list = tblColumnService.selectVariablesByArea(condition).getList();
        data.setData(list);
        return data;
    }

    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "get", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData get(@RequestParam String id) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        data.setData(tblColumnService.get(id));
        return data;
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "find", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData findList(@RequestBody TblColumnCondition condition) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        // 根据登录用户身份筛选集中器;总队（全部），支队（集中器的管理单位），维修人员（集中器的维修单位）
        User user = UserUtils.getUser();
        UserExt userExt = userExtService.get(user.getId());
        if (user.getUserType().equals(Constant.USER_TYPE.DIVISION))
        { // 总队
            condition.setDevisionId(user.getOffice().getId());
        }
        if (user.getUserType().equals(Constant.USER_TYPE.DETACHMENT))
        { // 支队
            condition.setDetachmentId(user.getOffice().getId());
        }
        if (user.getUserType().equals(Constant.USER_TYPE.SUPPORT) || user.getUserType().equals(Constant.USER_TYPE.SUPPORTOR))
        { // 维修
            condition.setSupportId(userExt.getSupportId());
        }
        data.setData(tblColumnService.selectVariablesByArea(condition));
        return data;
    }

    @RequiresPermissions({"signal:tblColumn:add","signal:brand:add"})
    @RequestMapping(value = "/addColumn", method = RequestMethod.POST)
    public Object addVariable(@RequestBody Map<String,Object> param){

        String code = param.get("code").toString();
        String listCode = tblColumnService.listCode(code);
        if(listCode!=null){
            return ResponseUtil.fail(403,"编码已存在");
        }
        return ResponseUtil.ok(tblColumnService.addColumn(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/resetShaft", method = RequestMethod.POST)
    public Object resetShaft(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.resetShaft(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateColumn", method = RequestMethod.POST)
    public Object updateColumn(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.updateColumn(param));
    }

    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateProjector", method = RequestMethod.POST)
    public Object updateProjector(@RequestBody List<Map<String,Object>> param){
        return ResponseUtil.ok(tblColumnService.updateProjector(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateHorn", method = RequestMethod.POST)
    public Object updateHorn(@RequestBody List<Map<String,Object>> param){
        return ResponseUtil.ok(tblColumnService.updateHorn(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateDisplay", method = RequestMethod.POST)
    public Object updateDisplay(@RequestBody List<Map<String,Object>> param){
        return ResponseUtil.ok(tblColumnService.updateDisplay(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateColumnConfig", method = RequestMethod.POST)
    public Object updateColumnConfig(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.updateColumnConfig(param));
    }
    @RequiresPermissions({"signal:tblColumn:edit","signal:brand:edit"})
    @RequestMapping(value = "/updateAttitude", method = RequestMethod.POST)
    public Object updateAttitude(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.updateAttitude(param));
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "/listProjector", method = RequestMethod.POST)
    public Object listProjector(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.listProjector(param));
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "/listHorn", method = RequestMethod.POST)
    public Object listHorn(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.listHorn(param));
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "/listDisplay", method = RequestMethod.POST)
    public Object listDisplay(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.listDisplay(param));
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "/listColumnConfig", method = RequestMethod.POST)
    public Object listColumnConfig(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.listColumnConfig(param));
    }
    @RequiresPermissions({"signal:tblColumn:delete","signal:brand:delete"})
    @RequestMapping(value = "/deleteColumn", method = RequestMethod.POST)
    public Object deleteColumn(@RequestBody Map<String,Object> param){
        return ResponseUtil.ok(tblColumnService.deleteColumn(param));
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = { "listFailureHistory", "" })
    public String evaluationStatistics(TblFailureForm form, Model model) {
        if (form == null) form = new TblFailureForm();

        // 日格格式化
        if (form.getCondition().getStartDate() == null || form.getCondition().getEndDate() == null) {
            Date d = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            form.getCondition().setStartDate(sdf.format(d) + " 00:00:00");
            form.getCondition().setEndDate(sdf.format(d) + " 23:59:59");
            form.getCondition().setPage(form.getCondition().getPage().setList(null));
        } else {
            form.getCondition().setPage(tblColumnService.getFailureHistorys(form.getCondition()));
        }
        // 数据
        model.addAttribute("form", form);
        return " ";
    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "listFailure", method = RequestMethod.POST)
    @ResponseBody
    public Object listFailure(@RequestBody TblFailureCondition condition) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        data.setData(tblColumnFailureService.findList(condition));
        return data;
    }

    @RequiresPermissions({"signal:tblColumn:put","signal:brand:put"})
    @RequestMapping(value = "/secondPutRed", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject secondPutRed(@RequestBody TblColumn collector)
    {
        try
        {
            daemonService.secondPutRed(collector.getCode());
            logger.info("红投影下放成功"+collector.getCode());
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(),"红投影下放失败"+collector.getCode());
            return ResponseUtil.fail(403,"红投影下放失败");
        }
        return ResponseUtil.ok(collector.getCode());

    }

    @RequiresPermissions({"signal:tblColumn:put","signal:brand:put"})
    @RequestMapping(value = "/secondPutGreen", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject secondPutGreen(@RequestBody TblCollector collector)
    {
        try
        {
            daemonService.secondPutGreen(collector.getCode());
            logger.info("绿投影下放成功"+collector.getCode());
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(),"绿投影下放失败"+collector.getCode());
            return ResponseUtil.fail(403,"绿投影下放失败");
        }
        return ResponseUtil.ok(collector.getCode());

    }
    @RequiresPermissions({"signal:tblColumn:put","signal:brand:put"})
    @RequestMapping(value = "/secondPutHorn", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject secondPutHorn(@RequestBody TblCollector collector)
    {
        try
        {
            daemonService.secondPutHorn(collector.getCode());
            logger.info("音响下发成功"+collector.getCode());
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(),"音响下放失败"+collector.getCode());
            return ResponseUtil.fail(403,"音响下放失败");
        }
        return ResponseUtil.ok(collector.getCode());

    }
    @RequiresPermissions({"signal:tblColumn:put","signal:brand:put"})
    @RequestMapping(value = "/secondPutDisplay", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject secondPutDisplay(@RequestBody TblCollector collector)
    {
        try
        {
            daemonService.secondPutDisplay(collector.getCode());
            logger.info("显示屏下放成功"+collector.getCode());
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(),"显示屏下放失败"+collector.getCode());
            return ResponseUtil.fail(403,"显示屏下放失败");
        }
        return ResponseUtil.ok(collector.getCode());

    }
    @RequiresPermissions({"signal:tblColumn:put","signal:brand:put"})
    @RequestMapping(value = "/secondPutAlarmConfig", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject secondPutAlarmConfig(@RequestBody TblCollector collector)
    {
        try
        {
            daemonService.secondPutAlarmConfig(collector.getCode());
            logger.info("心跳设置协议下放成功"+collector.getCode());
        }
        catch (Exception e)
        {
            logger.error(e.getMessage(),"心跳设置协议下放失败"+collector.getCode());
            return ResponseUtil.fail(403,"心跳设置协议下放失败");
        }
        return ResponseUtil.ok(collector.getCode());

    }
    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "getShiftThreshold", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData getThresholdXYZ(@RequestParam String collectorId) {
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        ShiftEntity shiftEntity = new ShiftEntity();
        Map<String, String> messages = new HashMap<>();
        if (!StringUtils.isEmpty(collectorId)) {
            TblColumn tblColumn = tblColumnService.getThresholdXYZ(collectorId);
            if (tblColumn != null){
                shiftEntity.setThresholdValuex(tblColumn.getThresholdvalueXshaft());
                shiftEntity.setThresholdValuey(tblColumn.getThresholdvalueYshaft());
                shiftEntity.setThresholdValuez(tblColumn.getThresholdvalueZshaft());
                BeanUtils.copyProperties(tblColumn, shiftEntity);
                messages.put("message", "查询成功");
                data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
                data.setData(shiftEntity);
                data.setMessages(messages);
                return data;
            }
            messages.put("message", "该集中器不存在");
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
            data.setMessages(messages);
            return data;
        }
        messages.put("message", "请选中某一个集中器");
        data.setStatus(AsyncResponseData.RESPONSE_STATUS_REQUEST_DENIED);
        data.setMessages(messages);
        return data;
    }

    @RequiresPermissions({"signal:tblColumn:view","signal:brand:view"})
    @RequestMapping(value = "listFailureHistorys", method = RequestMethod.POST)
    @ResponseBody
    public Object listFailure(@RequestBody Map<String,Object> param) {
        return tblColumnService.listFailureHistorys(param);
    }
    @RequestMapping(value = "findCode", method = RequestMethod.GET)
    @ResponseBody
    public List<String>  findCode() {

 return  tblColumnService.findCode();

    }
}