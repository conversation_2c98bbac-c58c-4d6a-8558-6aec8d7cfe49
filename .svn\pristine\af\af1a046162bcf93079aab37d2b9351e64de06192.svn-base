package com.lumlux.signal.daemon.mina.protocol;

import com.lumlux.signal.daemon.parser.heinqi.Constants;
import com.lumlux.signal.daemon.util.ByteUtil;
import org.apache.log4j.Logger;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.codec.CumulativeProtocolDecoder;
import org.apache.mina.filter.codec.ProtocolDecoderOutput;


public class SignalDataDecoder extends CumulativeProtocolDecoder {

    private transient Logger log = Logger.getLogger(SignalDataDecoder.class);
    /**
     * 返回值含义:
     * 1、当内容刚好时，返回false，告知父类接收下一批内容
     * 2、内容不够时需要下一批发过来的内容，此时返回false，这样父类 CumulativeProtocolDecoder
     * 会将内容放进IoSession中，等下次来数据后就自动拼`装再交给本类的doDecode
     * 3、当内容多时，返回true，因为需要再将本批数据进行读取，父类会将剩余的数据再次推送本类的doDecode方法
     */
    @Override
    public boolean doDecode(IoSession session, IoBuffer in, ProtocolDecoderOutput out) throws Exception {
//        System.out.println("缓冲区容/*量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position()); // 4096 22 0
//        System.out.println("[in.remaining()]*/ 1:" + in.remaining()); //22
        //  验证缓冲区有可操作的数据
        if (in.remaining() < 4)//是用来当拆包时候剩余长度小于4的时候的保护，不加容易出错
        {
            return false;
        }
//        System.out.println(in);
//        log.info(in.array());
//        System.out.println(in.array());
        log.info("[SignalDataDecoder] 接收数据:" + (in == null ? "null" :  ByteUtil.asHex(in.array())));
//        System.out.println(ByteUtil.asHex(in.array()));
//        System.out.println("接受数据时间戳 " + new Date());
      /*  int rem = limit - position;
        return rem > 0 ? rem : 0;*/
//         System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 0
//        System.out.println("[in.remaining()] 2:" + in.remaining());//22
        if (in.remaining() > 1) {
//            System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 0
//            System.out.println("[in.remaining()] 3:" + in.remaining());//2
            //以便后继的reset操作能恢复position位置
            in.mark();
            byte[] packageHeader = new byte[4];
            in.get(packageHeader, 0, 4);

            switch (packageHeader[0]) {
                case com.lumlux.signal.daemon.parser.hongdian.Constants.HEAD:
                    int packageSize = ByteUtil.byte2Int(packageHeader[2], packageHeader[3]);//22
                    //比较消息长度和实际收到的长度是否相等，这里-2是因为我们的消息头有个short值还没取
                    //int remaining =  in.remaining();
//                    System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 4
//                    System.out.println("[in.remaining()] 4:" + in.remaining());//18
//                    System.out.println("packageSize:"+packageSize);
                     if (packageSize-4 > in.remaining()) {
//                        System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());
//                        System.out.println("[in.remaining()] 5:" + in.remaining());
                        //出现断包，则重置恢复position位置到操作前,进入下一轮, 接收新数据，以拼凑成完整数据
                        in.reset();
                        return false;
                    } else {
                        //消息内容足够
                    	in.reset();
//                        System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 0
                    	byte packageEnd = in.get(packageSize - 1 + in.position());
                    	if(packageEnd != 0x7b){
                    		//过滤数据  直接扔掉
                    		in.reset();//重置恢复position位置到操作前
                            byte[] packArr = new byte[packageSize];
                            in.get(packArr, 0, packageSize);
                            IoBuffer buffer = IoBuffer.allocate(packageSize);
                            buffer.put(packArr);
                            buffer.flip();
                            return false;
                    	}
                        in.reset();//重置恢复position位置到操作前
//                        System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 0
                        byte[] packArr = new byte[packageSize];
                        in.get(packArr, 0, packageSize);
                        IoBuffer buffer = IoBuffer.allocate(packageSize);
                        buffer.put(packArr);
                        buffer.flip();
                        out.write(buffer);
//                        System.out.println("缓冲区容量："+in.capacity() + "缓冲区界限："+in.limit() +"缓冲区操作数位置："+in.position());// 4096 22 22
                        //走到这里会调用DefaultHandler的messageReceived方法

                        if (in.remaining() > 0) {//出现粘包，就让父类再调用一次，进行下一次解析
                            return true;
                        }
                    }
                    break;
                case Constants.HEAD:
                    int hqPkgLength = 15;
                    if(packageHeader[1]==0x1e)
                    {
                    	byte[] arr = new byte[10];
                        in.get(arr, 0, 10);
                    	hqPkgLength = 15 + 2 * arr[9];
                    }
                    else if(packageHeader[1]==0x20)
                    {
                    	hqPkgLength = 57;
                    }
                    else if(packageHeader[1]==0x32)
                    {
                    	hqPkgLength = 165;
                    } else if (packageHeader[1] ==0x62 || packageHeader[1] ==-74) {
                        hqPkgLength = 37;

                    }  else if(packageHeader[1]==0x64)
                    {
                        hqPkgLength = 100;

                    }   else if(packageHeader[1]==0x66)
                    {
                        hqPkgLength = 20;
                    }
                    else if(packageHeader[1]==0x40)
                    {
                    	byte[] arr = new byte[10];
                        in.get(arr, 0, 10);
                    	hqPkgLength = 15 + 8 * arr[9];
                    } else if (packageHeader[1] == 0x1d) {
                        hqPkgLength = 40;
//                        hqPkgLength = 62;
//                        hqPkgLength = 86;
                    } else if (packageHeader[1] == 0x57) {
//                        hqPkgLength = 40;
//                        hqPkgLength = 62;
                        hqPkgLength = 30;
                    } else if(packageHeader[1]==-79 ||packageHeader[1]==-76)
                    {
                        hqPkgLength = 38;

                    } else if(packageHeader[1]==-78)
                    {
                        hqPkgLength = 25;

                    }  else if(packageHeader[1]==-95)
                    {
                        hqPkgLength = 20;

                    } else if(packageHeader[1]==-94)
                    {
                        hqPkgLength = 16;

                    } else if(packageHeader[1]==-92||packageHeader[1]==-86)
                    {
                        hqPkgLength = 25;

                    } else if(packageHeader[1]==-91||packageHeader[1]==-85)
                    {
                        hqPkgLength = 36;

                    }  else if(packageHeader[1]==-90)
                    {
                        hqPkgLength = 34;

                    }else if(packageHeader[1]==-89)
                    {
                        hqPkgLength = 16;

                    }else if(packageHeader[1]==-88)
                    {
                        hqPkgLength = 16;

                    }else if (packageHeader[1]==-72) {
                        hqPkgLength = 15;
                    }

                    if (hqPkgLength-4 > in.remaining()) {
                        //出现断包，则重置恢复position位置到操作前,进入下一轮, 接收新数据，以拼凑成完整数据
                        in.reset();
                        return false;
                    }
                    byte[] packArr = new byte[hqPkgLength];
                    in.reset();
                    in.get(packArr, 0, hqPkgLength);
                    IoBuffer buffer = IoBuffer.allocate(hqPkgLength);
                    buffer.put(packArr);
                    buffer.flip();
                    out.write(buffer);
                    //走到这里会调用DefaultHandler的messageReceived方法
                    if (in.remaining() > 0) {//出现粘包，就让父类再调用一次，进行下一次解析
                        return true;
                    }
                    break;
                default:
                    byte[] defaultByte = new byte[in.limit()];
                    in.reset();
                    in.get(defaultByte, 0, in.limit());
                    return false;
            }

        }
        return false;//处理成功，让父类进行接收下个包
    }
}