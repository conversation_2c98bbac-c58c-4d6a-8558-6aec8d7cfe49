package com.lumlux.signal.daemon.bean;

import java.util.Date;

public class TblCollector {

	private String id;

	private String name;

	private String model;
	
	private String code;

	private String areaId;

	private Byte boardQuantity;

	private String longitude;

	private String latitude;

	private String geoHashCode;

	private String pictureUrl;

	private String enabled;

	private String status;

	private String failureStatus;

	private String supplierId;

	private String supportId;

	private String officeId;

	private String simNo;

	private String installPosition;

	private String createBy;

	private Date createDate;

	private String updateBy;

	private Date updateDate;

	private String remarks;

	private Character delFlag;

	private String address;
	
	private String powerMode;
	
	private String relayStatus;
	
	private Date failureDate;
	
	private String version;
	
	private String controlMode;
	
	private String isInform;
	
	private String readTime;
	
	private String ledCount;
	
	private String lineCount;
	
	private String reduceTime;
	
	private String isReduce;

	private int onlineMinutes;

	private String putFlag;


	private int counter;


	private String timeSpanA;
	private String timeSpanB;

	private int magneticLockGoal;

//	private Date activationTime;

/*	public Date getActivationTime() {
		return activationTime;
	}

	public void setActivationTime(Date activationTime) {
		this.activationTime = activationTime;
	}*/

	public int getMagneticLockGoal() {
		return magneticLockGoal;
	}

	public void setMagneticLockGoal(int magneticLockGoal) {
		this.magneticLockGoal = magneticLockGoal;
	}

	public String getTimeSpanA() {
		return timeSpanA;
	}

	public void setTimeSpanA(String timeSpanA) {
		this.timeSpanA = timeSpanA;
	}

	public String getTimeSpanB() {
		return timeSpanB;
	}

	public void setTimeSpanB(String timeSpanB) {
		this.timeSpanB = timeSpanB;
	}

	public int getCounter() {
		return counter;
	}

	public void setCounter(int counter) {
		this.counter = counter;
	}

	public String getPutFlag() {
		return putFlag;
	}

	public void setPutFlag(String putFlag) {
		this.putFlag = putFlag;
	}

	public int getOnlineMinutes() {
		return onlineMinutes;
	}

	public void setOnlineMinutes(int onlineMinutes) {
		this.onlineMinutes = onlineMinutes;
	}

	public String getIsReduce()
	{
		return isReduce;
	}

	public void setIsReduce(String isReduce)
	{
		this.isReduce = isReduce;
	}

	public String getLedCount()
	{
		return ledCount;
	}

	public void setLedCount(String ledCount)
	{
		this.ledCount = ledCount;
	}

	public String getLineCount()
	{
		return lineCount;
	}

	public void setLineCount(String lineCount)
	{
		this.lineCount = lineCount;
	}

	public String getReduceTime()
	{
		return reduceTime;
	}

	public void setReduceTime(String reduceTime)
	{
		this.reduceTime = reduceTime;
	}

	public String getReadTime()
	{
		return readTime;
	}

	public void setReadTime(String readTime)
	{
		this.readTime = readTime;
	}
	public String getIsInform()
	{
		return isInform;
	}

	public void setIsInform(String isInform)
	{
		this.isInform = isInform;
	}

	public String getControlMode()
	{
		return controlMode;
	}

	public void setControlMode(String controlMode)
	{
		this.controlMode = controlMode;
	}

	public String getVersion()
	{
		return version;
	}

	public void setVersion(String version)
	{
		this.version = version;
	}
	
	public Date getFailureDate()
	{
		return failureDate;
	}

	public void setFailureDate(Date failureDate)
	{
		this.failureDate = failureDate;
	}
	
	public String getRelayStatus()
	{
		return relayStatus;
	}

	public void setRelayStatus(String relayStatus)
	{
		this.relayStatus = relayStatus;
	}

	public String getPowerMode()
	{
		return powerMode;
	}

	public void setPowerMode(String powerMode)
	{
		this.powerMode = powerMode;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getAreaId() {
		return this.areaId;
	}

	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}

	public Byte getBoardQuantity() {
		return this.boardQuantity;
	}

	public void setBoardQuantity(Byte boardQuantity) {
		this.boardQuantity = boardQuantity;
	}

	public String getLongitude() {
		return this.longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return this.latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getGeoHashCode() {
		return this.geoHashCode;
	}

	public void setGeoHashCode(String geoHashCode) {
		this.geoHashCode = geoHashCode;
	}

	public String getPictureUrl() {
		return this.pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	public String getEnabled() {
		return enabled;
	}

	public void setEnabled(String enabled) {
		this.enabled = enabled;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getFailureStatus() {
		return failureStatus;
	}

	public void setFailureStatus(String failureStatus) {
		this.failureStatus = failureStatus;
	}

	public String getSupplierId() {
		return this.supplierId;
	}

	public void setSupplierId(String supplierId) {
		this.supplierId = supplierId;
	}

	public String getSupportId() {
		return this.supportId;
	}

	public void setSupportId(String supportId) {
		this.supportId = supportId;
	}

	public String getOfficeId() {
		return this.officeId;
	}

	public void setOfficeId(String officeId) {
		this.officeId = officeId;
	}

	public String getSimNo() {
		return this.simNo;
	}

	public void setSimNo(String simNo) {
		this.simNo = simNo;
	}

	public String getInstallPosition() {
		return this.installPosition;
	}

	public void setInstallPosition(String installPosition) {
		this.installPosition = installPosition;
	}

	public String getCreateBy() {
		return this.createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getUpdateBy() {
		return this.updateBy;
	}

	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getRemarks() {
		return this.remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public Character getDelFlag() {
		return this.delFlag;
	}

	public void setDelFlag(Character delFlag) {
		this.delFlag = delFlag;
	}

	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getModel()
	{
		return model;
	}

	public void setModel(String model)
	{
		this.model = model;
	}

	public String toString() {
		return "" + this.getId() + "\t" + this.getName()+ "\t" + this.getModel() + "\t" + this.getCode() + "\t" + this.getAreaId() + "\t"
				+ this.getBoardQuantity() + "\t" + this.getLongitude() + "\t" + this.getLatitude() + "\t"
				+ this.getGeoHashCode() + "\t" + this.getPictureUrl() + "\t" + this.getEnabled() + "\t"
				+ this.getStatus() + "\t" + this.getFailureStatus() + "\t" + this.getSupplierId() + "\t"
				+ this.getSupportId() + "\t" + this.getOfficeId() + "\t" + this.getSimNo() + "\t"
				+ this.getInstallPosition() + "\t" + this.getCreateBy() + "\t" + this.getCreateDate() + "\t"
				+ this.getUpdateBy() + "\t" + this.getUpdateDate() + "\t" + this.getRemarks() + "\t" + this.getDelFlag()
				+ "\t" + this.getAddress() + "\t";
	}

}
