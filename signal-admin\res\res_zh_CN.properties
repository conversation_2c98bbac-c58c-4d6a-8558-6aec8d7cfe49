# FunctionList
FUNCTIONLIST=\u529F\u80FD\u5217\u8868
REALPLAY=\u5B9E\u65F6\u9884\u89C8
CAPTURE_PICTURE=\u6293\u62CD\u56FE\u7247
MULTIREALPLAY=\u53CC\u901A\u9053\u5B9E\u65F6\u9884\u89C8
DOWNLOAD_RECORD=\u4E0B\u8F7D\u5F55\u50CF
ITS_EVENT=\u667A\u80FD\u4EA4\u901A
TALK=\u8BED\u97F3\u5BF9\u8BB2
DEVICESEARCH_DEVICEINIT=\u8BBE\u5907\u641C\u7D22\u548C\u8BBE\u5907\u521D\u59CB\u5316
PTZ=\u4E91\u53F0\u63A7\u5236
FACERECOGNITION=\u4EBA\u8138\u8BC6\u522B
ALARM_LISTEN=\u62A5\u8B66\u76D1\u542C
DEVICE_CONTROL=\u8BBE\u5907\u63A7\u5236
AUTOREGISTER=\u4E3B\u52A8\u6CE8\u518C

ONLINE=\u5728\u7EBF

# Login Info
DEVICE_IP=\u8BBE\u5907\u5730\u5740
DEVICE_PORT=\u7AEF\u53E3\u53F7
USERNAME=\u7528\u6237\u540D
PASSWORD=\u5BC6\u7801
IP=\u5730\u5740

LOGIN=\u767B\u5F55
LOGOUT=\u767B\u51FA

LOGIN_SUCCEED=\u767B\u5F55\u6210\u529F
LOGIN_FAILED=\u767B\u5F55\u5931\u8D25
DISCONNECT=\u8BBE\u5907\u65AD\u7EBF
DISCONNECT_RECONNECTING=\u8BBE\u5907\u65AD\u7EBF\uFF0C\u6B63\u5728\u91CD\u8FDE\u4E2D
PROMPT_MESSAGE=\u63D0\u793A\u4FE1\u606F
ERROR_MESSAGE=\u9519\u8BEF\u4FE1\u606F

SUCCEED=\u6210\u529F
FAILED=\u5931\u8D25

PLEASE_INPUT_DEVICE_IP=\u8BF7\u8F93\u5165\u8BBE\u5907\u5730\u5740
PLEASE_INPUT_DEVICE_PORT=\u8BF7\u8F93\u5165\u8BBE\u5907\u7AEF\u53E3\u53F7
PLEASE_INPUT_DEVICE_USERNAME=\u8BF7\u8F93\u5165\u7528\u6237\u540D
PLEASE_INPUT_DEVICE_PASSWORD=\u8BF7\u8F93\u5165\u5BC6\u7801
PLEASE_INPUT_CONFIRM_PASSWORD=\u8BF7\u8F93\u5165\u786E\u8BA4\u5BC6\u7801

# Error Info
NET_NOERROR=\u6CA1\u6709\u9519\u8BEF
NET_ERROR=\u672A\u77E5\u9519\u8BEF
NET_SYSTEM_ERROR=Windows\u7CFB\u7EDF\u51FA\u9519
NET_NETWORK_ERROR=\u7F51\u7EDC\u9519\u8BEF\uFF0C\u53EF\u80FD\u662F\u56E0\u4E3A\u7F51\u7EDC\u8D85\u65F6
NET_DEV_VER_NOMATCH=\u8BBE\u5907\u534F\u8BAE\u4E0D\u5339\u914D
NET_INVALID_HANDLE=\u53E5\u67C4\u65E0\u6548
NET_OPEN_CHANNEL_ERROR=\u6253\u5F00\u901A\u9053\u5931\u8D25
NET_CLOSE_CHANNEL_ERROR=\u5173\u95ED\u901A\u9053\u5931\u8D25
NET_ILLEGAL_PARAM=\u7528\u6237\u53C2\u6570\u4E0D\u5408\u6CD5
NET_SDK_INIT_ERROR=SDK\u521D\u59CB\u5316\u51FA\u9519
NET_SDK_UNINIT_ERROR=SDK\u6E05\u7406\u51FA\u9519
NET_RENDER_OPEN_ERROR=\u7533\u8BF7render\u8D44\u6E90\u51FA\u9519
NET_DEC_OPEN_ERROR=\u6253\u5F00\u89E3\u7801\u5E93\u51FA\u9519
NET_DEC_CLOSE_ERROR=\u5173\u95ED\u89E3\u7801\u5E93\u51FA\u9519
NET_MULTIPLAY_NOCHANNEL=\u591A\u753B\u9762\u9884\u89C8\u4E2D\u68C0\u6D4B\u5230\u901A\u9053\u6570\u4E3A0
NET_TALK_INIT_ERROR=\u5F55\u97F3\u5E93\u521D\u59CB\u5316\u5931\u8D25
NET_TALK_NOT_INIT=\u5F55\u97F3\u5E93\u672A\u7ECF\u521D\u59CB\u5316
NET_TALK_SENDDATA_ERROR=\u53D1\u9001\u97F3\u9891\u6570\u636E\u51FA\u9519
NET_REAL_ALREADY_SAVING=\u5B9E\u65F6\u6570\u636E\u5DF2\u7ECF\u5904\u4E8E\u4FDD\u5B58\u72B6\u6001
NET_NOT_SAVING=\u672A\u4FDD\u5B58\u5B9E\u65F6\u6570\u636E
NET_OPEN_FILE_ERROR=\u6253\u5F00\u6587\u4EF6\u51FA\u9519
NET_PTZ_SET_TIMER_ERROR=\u542F\u52A8\u4E91\u53F0\u63A7\u5236\u5B9A\u65F6\u5668\u5931\u8D25
NET_RETURN_DATA_ERROR=\u5BF9\u8FD4\u56DE\u6570\u636E\u7684\u6821\u9A8C\u51FA\u9519
NET_INSUFFICIENT_BUFFER=\u6CA1\u6709\u8DB3\u591F\u7684\u7F13\u5B58
NET_NOT_SUPPORTED=\u5F53\u524DSDK\u672A\u652F\u6301\u8BE5\u529F\u80FD
NET_NO_RECORD_FOUND=\u67E5\u8BE2\u4E0D\u5230\u5F55\u50CF
NET_NOT_AUTHORIZED=\u65E0\u64CD\u4F5C\u6743\u9650
NET_NOT_NOW=\u6682\u65F6\u65E0\u6CD5\u6267\u884C
NET_NO_TALK_CHANNEL=\u672A\u53D1\u73B0\u5BF9\u8BB2\u901A\u9053
NET_NO_AUDIO=\u672A\u53D1\u73B0\u97F3\u9891
NET_NO_INIT=\u7F51\u7EDCSDK\u672A\u7ECF\u521D\u59CB\u5316
NET_DOWNLOAD_END=\u4E0B\u8F7D\u5DF2\u7ED3\u675F
NET_EMPTY_LIST=\u67E5\u8BE2\u7ED3\u679C\u4E3A\u7A7A
NET_ERROR_GETCFG_SYSATTR=\u83B7\u53D6\u7CFB\u7EDF\u5C5E\u6027\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_SERIAL=\u83B7\u53D6\u5E8F\u5217\u53F7\u5931\u8D25
NET_ERROR_GETCFG_GENERAL=\u83B7\u53D6\u5E38\u89C4\u5C5E\u6027\u5931\u8D25
NET_ERROR_GETCFG_DSPCAP=\u83B7\u53D6DSP\u80FD\u529B\u63CF\u8FF0\u5931\u8D25
NET_ERROR_GETCFG_NETCFG=\u83B7\u53D6\u7F51\u7EDC\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_CHANNAME=\u83B7\u53D6\u901A\u9053\u540D\u79F0\u5931\u8D25
NET_ERROR_GETCFG_VIDEO=\u83B7\u53D6\u89C6\u9891\u5C5E\u6027\u5931\u8D25
NET_ERROR_GETCFG_RECORD=\u83B7\u53D6\u5F55\u8C61\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_PRONAME=\u83B7\u53D6\u89E3\u7801\u5668\u534F\u8BAE\u540D\u79F0\u5931\u8D25
NET_ERROR_GETCFG_FUNCNAME=\u83B7\u53D6232\u4E32\u53E3\u529F\u80FD\u540D\u79F0\u5931\u8D25
NET_ERROR_GETCFG_485DECODER=\u83B7\u53D6\u89E3\u7801\u5668\u5C5E\u6027\u5931\u8D25
NET_ERROR_GETCFG_232COM=\u83B7\u53D6232\u4E32\u53E3\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_ALARMIN=\u83B7\u53D6\u5916\u90E8\u62A5\u8B66\u8F93\u5165\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_ALARMDET=\u83B7\u53D6\u52A8\u6001\u68C0\u6D4B\u62A5\u8B66\u5931\u8D25
NET_ERROR_GETCFG_SYSTIME=\u83B7\u53D6\u8BBE\u5907\u65F6\u95F4\u5931\u8D25
NET_ERROR_GETCFG_PREVIEW=\u83B7\u53D6\u9884\u89C8\u53C2\u6570\u5931\u8D25
NET_ERROR_GETCFG_AUTOMT=\u83B7\u53D6\u81EA\u52A8\u7EF4\u62A4\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_VIDEOMTRX=\u83B7\u53D6\u89C6\u9891\u77E9\u9635\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_COVER=\u83B7\u53D6\u533A\u57DF\u906E\u6321\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_WATERMAKE=\u83B7\u53D6\u56FE\u8C61\u6C34\u5370\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_MULTICAST=\u83B7\u53D6\u914D\u7F6E\u5931\u8D25\u4F4D\u7F6E\uFF1A\u7EC4\u64AD\u7AEF\u53E3\u6309\u901A\u9053\u914D\u7F6E
NET_ERROR_SETCFG_GENERAL=\u4FEE\u6539\u5E38\u89C4\u5C5E\u6027\u5931\u8D25
NET_ERROR_SETCFG_NETCFG=\u4FEE\u6539\u7F51\u7EDC\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_CHANNAME=\u4FEE\u6539\u901A\u9053\u540D\u79F0\u5931\u8D25
NET_ERROR_SETCFG_VIDEO=\u4FEE\u6539\u89C6\u9891\u5C5E\u6027\u5931\u8D25
NET_ERROR_SETCFG_RECORD=\u4FEE\u6539\u5F55\u8C61\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_485DECODER=\u4FEE\u6539\u89E3\u7801\u5668\u5C5E\u6027\u5931\u8D25
NET_ERROR_SETCFG_232COM=\u4FEE\u6539232\u4E32\u53E3\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_ALARMIN=\u4FEE\u6539\u5916\u90E8\u8F93\u5165\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_ALARMDET=\u4FEE\u6539\u52A8\u6001\u68C0\u6D4B\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_SYSTIME=\u4FEE\u6539\u8BBE\u5907\u65F6\u95F4\u5931\u8D25
NET_ERROR_SETCFG_PREVIEW=\u4FEE\u6539\u9884\u89C8\u53C2\u6570\u5931\u8D25
NET_ERROR_SETCFG_AUTOMT=\u4FEE\u6539\u81EA\u52A8\u7EF4\u62A4\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_VIDEOMTRX=\u4FEE\u6539\u89C6\u9891\u77E9\u9635\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_COVER=\u4FEE\u6539\u533A\u57DF\u906E\u6321\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_WATERMAKE=\u4FEE\u6539\u56FE\u8C61\u6C34\u5370\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_WLAN=\u4FEE\u6539\u65E0\u7EBF\u7F51\u7EDC\u4FE1\u606F\u5931\u8D25
NET_ERROR_SETCFG_WLANDEV=\u9009\u62E9\u65E0\u7EBF\u7F51\u7EDC\u8BBE\u5907\u5931\u8D25
NET_ERROR_SETCFG_REGISTER=\u4FEE\u6539\u4E3B\u52A8\u6CE8\u518C\u53C2\u6570\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_CAMERA=\u4FEE\u6539\u6444\u50CF\u5934\u5C5E\u6027\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_INFRARED=\u4FEE\u6539\u7EA2\u5916\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_SOUNDALARM=\u4FEE\u6539\u97F3\u9891\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_STORAGE=\u4FEE\u6539\u5B58\u50A8\u4F4D\u7F6E\u914D\u7F6E\u5931\u8D25
NET_AUDIOENCODE_NOTINIT=\u97F3\u9891\u7F16\u7801\u63A5\u53E3\u6CA1\u6709\u6210\u529F\u521D\u59CB\u5316
NET_DATA_TOOLONGH=\u6570\u636E\u8FC7\u957F
NET_UNSUPPORTED=\u8BBE\u5907\u4E0D\u652F\u6301\u8BE5\u64CD\u4F5C
NET_DEVICE_BUSY=\u8BBE\u5907\u8D44\u6E90\u4E0D\u8DB3
NET_SERVER_STARTED=\u670D\u52A1\u5668\u5DF2\u7ECF\u542F\u52A8
NET_SERVER_STOPPED=\u670D\u52A1\u5668\u5C1A\u672A\u6210\u529F\u542F\u52A8
NET_LISTER_INCORRECT_SERIAL=\u8F93\u5165\u5E8F\u5217\u53F7\u6709\u8BEF
NET_QUERY_DISKINFO_FAILED=\u83B7\u53D6\u786C\u76D8\u4FE1\u606F\u5931\u8D25
NET_ERROR_GETCFG_SESSION=\u83B7\u53D6\u8FDE\u63A5Session\u4FE1\u606F
NET_USER_FLASEPWD_TRYTIME=\u8F93\u5165\u5BC6\u7801\u9519\u8BEF\u8D85\u8FC7\u9650\u5236\u6B21\u6570
NET_LOGIN_ERROR_PASSWORD=\u5BC6\u7801\u4E0D\u6B63\u786E
NET_LOGIN_ERROR_USER=\u5E10\u6237\u4E0D\u5B58\u5728
NET_LOGIN_ERROR_TIMEOUT=\u7B49\u5F85\u767B\u5F55\u8FD4\u56DE\u8D85\u65F6
NET_LOGIN_ERROR_RELOGGIN=\u5E10\u53F7\u5DF2\u767B\u5F55
NET_LOGIN_ERROR_LOCKED=\u5E10\u53F7\u5DF2\u88AB\u9501\u5B9A
NET_LOGIN_ERROR_BLACKLIST=\u5E10\u53F7\u5DF2\u88AB\u5217\u4E3A\u9ED1\u540D\u5355
NET_LOGIN_ERROR_BUSY=\u8D44\u6E90\u4E0D\u8DB3\uFF0C\u7CFB\u7EDF\u5FD9
NET_LOGIN_ERROR_CONNECT=\u767B\u5F55\u8BBE\u5907\u8D85\u65F6\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u5E76\u91CD\u8BD5
NET_LOGIN_ERROR_NETWORK=\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25
NET_LOGIN_ERROR_SUBCONNECT=\u767B\u5F55\u8BBE\u5907\u6210\u529F\uFF0C\u4F46\u65E0\u6CD5\u521B\u5EFA\u89C6\u9891\u901A\u9053\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u72B6\u51B5
NET_LOGIN_ERROR_MAXCONNECT=\u8D85\u8FC7\u6700\u5927\u8FDE\u63A5\u6570
NET_LOGIN_ERROR_PROTOCOL3_ONLY=\u53EA\u652F\u63013\u4EE3\u534F\u8BAE
NET_LOGIN_ERROR_UKEY_LOST=\u672A\u63D2\u5165U\u76FE\u6216U\u76FE\u4FE1\u606F\u9519\u8BEF
NET_LOGIN_ERROR_NO_AUTHORIZED=\u5BA2\u6237\u7AEFIP\u5730\u5740\u6CA1\u6709\u767B\u5F55\u6743\u9650
NET_LOGIN_ERROR_USER_OR_PASSOWRD=\u8D26\u53F7\u6216\u5BC6\u7801\u9519\u8BEF
NET_LOGIN_ERROR_DEVICE_NOT_INIT=\u8BBE\u5907\u5C1A\u672A\u521D\u59CB\u5316\uFF0C\u4E0D\u80FD\u767B\u5F55\uFF0C\u8BF7\u5148\u521D\u59CB\u5316\u8BBE\u5907
NET_RENDER_SOUND_ON_ERROR=Render\u5E93\u6253\u5F00\u97F3\u9891\u51FA\u9519
NET_RENDER_SOUND_OFF_ERROR=Render\u5E93\u5173\u95ED\u97F3\u9891\u51FA\u9519
NET_RENDER_SET_VOLUME_ERROR=Render\u5E93\u63A7\u5236\u97F3\u91CF\u51FA\u9519
NET_RENDER_ADJUST_ERROR=Render\u5E93\u8BBE\u7F6E\u753B\u9762\u53C2\u6570\u51FA\u9519
NET_RENDER_PAUSE_ERROR=Render\u5E93\u6682\u505C\u64AD\u653E\u51FA\u9519
NET_RENDER_SNAP_ERROR=Render\u5E93\u6293\u56FE\u51FA\u9519
NET_RENDER_STEP_ERROR=Render\u5E93\u6B65\u8FDB\u51FA\u9519
NET_RENDER_FRAMERATE_ERROR=Render\u5E93\u8BBE\u7F6E\u5E27\u7387\u51FA\u9519
NET_RENDER_DISPLAYREGION_ERROR=Render\u5E93\u8BBE\u7F6E\u663E\u793A\u533A\u57DF\u51FA\u9519
NET_RENDER_GETOSDTIME_ERROR=Render\u5E93\u83B7\u53D6\u5F53\u524D\u64AD\u653E\u65F6\u95F4\u51FA\u9519
NET_GROUP_EXIST=\u7EC4\u540D\u5DF2\u5B58\u5728
NET_GROUP_NOEXIST=\u7EC4\u540D\u4E0D\u5B58\u5728
NET_GROUP_RIGHTOVER=\u7EC4\u7684\u6743\u9650\u8D85\u51FA\u6743\u9650\u5217\u8868\u8303\u56F4
NET_GROUP_HAVEUSER=\u7EC4\u4E0B\u6709\u7528\u6237\uFF0C\u4E0D\u80FD\u5220\u9664
NET_GROUP_RIGHTUSE=\u7EC4\u7684\u67D0\u4E2A\u6743\u9650\u88AB\u7528\u6237\u4F7F\u7528\uFF0C\u4E0D\u80FD\u5220\u9664
NET_GROUP_SAMENAME=\u65B0\u7EC4\u540D\u540C\u5DF2\u6709\u7EC4\u540D\u91CD\u590D
NET_USER_EXIST=\u7528\u6237\u5DF2\u5B58\u5728
NET_USER_NOEXIST=\u7528\u6237\u4E0D\u5B58\u5728
NET_USER_RIGHTOVER=\u7528\u6237\u6743\u9650\u8D85\u51FA\u7EC4\u6743\u9650
NET_USER_PWD=\u4FDD\u7559\u5E10\u53F7\uFF0C\u4E0D\u5BB9\u8BB8\u4FEE\u6539\u5BC6\u7801
NET_USER_FLASEPWD=\u5BC6\u7801\u4E0D\u6B63\u786E
NET_USER_NOMATCHING=\u5BC6\u7801\u4E0D\u5339\u914D
NET_USER_INUSE=\u8D26\u53F7\u6B63\u5728\u4F7F\u7528\u4E2D
NET_ERROR_GETCFG_ETHERNET=\u83B7\u53D6\u7F51\u5361\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_WLAN=\u83B7\u53D6\u65E0\u7EBF\u7F51\u7EDC\u4FE1\u606F\u5931\u8D25
NET_ERROR_GETCFG_WLANDEV=\u83B7\u53D6\u65E0\u7EBF\u7F51\u7EDC\u8BBE\u5907\u5931\u8D25
NET_ERROR_GETCFG_REGISTER=\u83B7\u53D6\u4E3B\u52A8\u6CE8\u518C\u53C2\u6570\u5931\u8D25
NET_ERROR_GETCFG_CAMERA=\u83B7\u53D6\u6444\u50CF\u5934\u5C5E\u6027\u5931\u8D25
NET_ERROR_GETCFG_INFRARED=\u83B7\u53D6\u7EA2\u5916\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_SOUNDALARM=\u83B7\u53D6\u97F3\u9891\u62A5\u8B66\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_STORAGE=\u83B7\u53D6\u5B58\u50A8\u4F4D\u7F6E\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_MAIL=\u83B7\u53D6\u90AE\u4EF6\u914D\u7F6E\u5931\u8D25
NET_CONFIG_DEVBUSY=\u6682\u65F6\u65E0\u6CD5\u8BBE\u7F6E
NET_CONFIG_DATAILLEGAL=\u914D\u7F6E\u6570\u636E\u4E0D\u5408\u6CD5
NET_ERROR_GETCFG_DST=\u83B7\u53D6\u590F\u4EE4\u65F6\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_DST=\u8BBE\u7F6E\u590F\u4EE4\u65F6\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_VIDEO_OSD=\u83B7\u53D6\u89C6\u9891OSD\u53E0\u52A0\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_VIDEO_OSD=\u8BBE\u7F6E\u89C6\u9891OSD\u53E0\u52A0\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_GPRSCDMA=\u83B7\u53D6CDMA\GPRS\u7F51\u7EDC\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_GPRSCDMA=\u8BBE\u7F6ECDMA\GPRS\u7F51\u7EDC\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_IPFILTER=\u83B7\u53D6IP\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_IPFILTER=\u8BBE\u7F6EIP\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_TALKENCODE=\u83B7\u53D6\u8BED\u97F3\u5BF9\u8BB2\u7F16\u7801\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_TALKENCODE=\u8BBE\u7F6E\u8BED\u97F3\u5BF9\u8BB2\u7F16\u7801\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_RECORDLEN=\u83B7\u53D6\u5F55\u50CF\u6253\u5305\u957F\u5EA6\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_RECORDLEN=\u8BBE\u7F6E\u5F55\u50CF\u6253\u5305\u957F\u5EA6\u914D\u7F6E\u5931\u8D25
NET_DONT_SUPPORT_SUBAREA=\u4E0D\u652F\u6301\u7F51\u7EDC\u786C\u76D8\u5206\u533A
NET_ERROR_GET_AUTOREGSERVER=\u83B7\u53D6\u8BBE\u5907\u4E0A\u4E3B\u52A8\u6CE8\u518C\u670D\u52A1\u5668\u4FE1\u606F\u5931\u8D25
NET_ERROR_CONTROL_AUTOREGISTER=\u4E3B\u52A8\u6CE8\u518C\u91CD\u5B9A\u5411\u6CE8\u518C\u9519\u8BEF
NET_ERROR_DISCONNECT_AUTOREGISTER=\u65AD\u5F00\u4E3B\u52A8\u6CE8\u518C\u670D\u52A1\u5668\u9519\u8BEF
NET_ERROR_GETCFG_MMS=\u83B7\u53D6mms\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_MMS=\u8BBE\u7F6Emms\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_SMSACTIVATION=\u83B7\u53D6\u77ED\u4FE1\u6FC0\u6D3B\u65E0\u7EBF\u8FDE\u63A5\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_SMSACTIVATION=\u8BBE\u7F6E\u77ED\u4FE1\u6FC0\u6D3B\u65E0\u7EBF\u8FDE\u63A5\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_DIALINACTIVATION=\u83B7\u53D6\u62E8\u53F7\u6FC0\u6D3B\u65E0\u7EBF\u8FDE\u63A5\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_DIALINACTIVATION=\u8BBE\u7F6E\u62E8\u53F7\u6FC0\u6D3B\u65E0\u7EBF\u8FDE\u63A5\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_VIDEOOUT=\u67E5\u8BE2\u89C6\u9891\u8F93\u51FA\u53C2\u6570\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_VIDEOOUT=\u8BBE\u7F6E\u89C6\u9891\u8F93\u51FA\u53C2\u6570\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_OSDENABLE=\u83B7\u53D6osd\u53E0\u52A0\u4F7F\u80FD\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_OSDENABLE=\u8BBE\u7F6Eosd\u53E0\u52A0\u4F7F\u80FD\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_ENCODERINFO=\u8BBE\u7F6E\u6570\u5B57\u901A\u9053\u524D\u7AEF\u7F16\u7801\u63A5\u5165\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_TVADJUST=\u83B7\u53D6TV\u8C03\u8282\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_TVADJUST=\u8BBE\u7F6ETV\u8C03\u8282\u914D\u7F6E\u5931\u8D25
NET_ERROR_CONNECT_FAILED=\u8BF7\u6C42\u5EFA\u7ACB\u8FDE\u63A5\u5931\u8D25
NET_ERROR_SETCFG_BURNFILE=\u8BF7\u6C42\u523B\u5F55\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
NET_ERROR_SNIFFER_GETCFG=\u83B7\u53D6\u6293\u5305\u914D\u7F6E\u4FE1\u606F\u5931\u8D25
NET_ERROR_SNIFFER_SETCFG=\u8BBE\u7F6E\u6293\u5305\u914D\u7F6E\u4FE1\u606F\u5931\u8D25
NET_ERROR_DOWNLOADRATE_GETCFG=\u67E5\u8BE2\u4E0B\u8F7D\u9650\u5236\u4FE1\u606F\u5931\u8D25
NET_ERROR_DOWNLOADRATE_SETCFG=\u8BBE\u7F6E\u4E0B\u8F7D\u9650\u5236\u4FE1\u606F\u5931\u8D25
NET_ERROR_SEARCH_TRANSCOM=\u67E5\u8BE2\u4E32\u53E3\u53C2\u6570\u5931\u8D25
NET_ERROR_GETCFG_POINT=\u83B7\u53D6\u9884\u5236\u70B9\u4FE1\u606F\u9519\u8BEF
NET_ERROR_SETCFG_POINT=\u8BBE\u7F6E\u9884\u5236\u70B9\u4FE1\u606F\u9519\u8BEF
NET_SDK_LOGOUT_ERROR=SDK\u6CA1\u6709\u6B63\u5E38\u767B\u51FA\u8BBE\u5907
NET_ERROR_GET_VEHICLE_CFG=\u83B7\u53D6\u8F66\u8F7D\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_VEHICLE_CFG=\u8BBE\u7F6E\u8F66\u8F7D\u914D\u7F6E\u5931\u8D25
NET_ERROR_GET_ATM_OVERLAY_CFG=\u83B7\u53D6atm\u53E0\u52A0\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_ATM_OVERLAY_CFG=\u8BBE\u7F6Eatm\u53E0\u52A0\u914D\u7F6E\u5931\u8D25
NET_ERROR_GET_ATM_OVERLAY_ABILITY=\u83B7\u53D6atm\u53E0\u52A0\u80FD\u529B\u5931\u8D25
NET_ERROR_GET_DECODER_TOUR_CFG=\u83B7\u53D6\u89E3\u7801\u5668\u89E3\u7801\u8F6E\u5DE1\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_DECODER_TOUR_CFG=\u8BBE\u7F6E\u89E3\u7801\u5668\u89E3\u7801\u8F6E\u5DE1\u914D\u7F6E\u5931\u8D25
NET_ERROR_CTRL_DECODER_TOUR=\u63A7\u5236\u89E3\u7801\u5668\u89E3\u7801\u8F6E\u5DE1\u5931\u8D25
NET_GROUP_OVERSUPPORTNUM=\u8D85\u51FA\u8BBE\u5907\u652F\u6301\u6700\u5927\u7528\u6237\u7EC4\u6570\u76EE
NET_USER_OVERSUPPORTNUM=\u8D85\u51FA\u8BBE\u5907\u652F\u6301\u6700\u5927\u7528\u6237\u6570\u76EE
NET_ERROR_GET_SIP_CFG=\u83B7\u53D6SIP\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_SIP_CFG=\u8BBE\u7F6ESIP\u914D\u7F6E\u5931\u8D25
NET_ERROR_GET_SIP_ABILITY=\u83B7\u53D6SIP\u80FD\u529B\u5931\u8D25
NET_ERROR_GET_WIFI_AP_CFG=\u83B7\u53D6WIFIap\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_WIFI_AP_CFG=\u8BBE\u7F6EWIFIap\u914D\u7F6E\u5931\u8D25
NET_ERROR_GET_DECODE_POLICY=\u83B7\u53D6\u89E3\u7801\u7B56\u7565\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_DECODE_POLICY=\u8BBE\u7F6E\u89E3\u7801\u7B56\u7565\u914D\u7F6E\u5931\u8D25
NET_ERROR_TALK_REJECT=\u62D2\u7EDD\u5BF9\u8BB2
NET_ERROR_TALK_OPENED=\u5BF9\u8BB2\u88AB\u5176\u4ED6\u5BA2\u6237\u7AEF\u6253\u5F00
NET_ERROR_TALK_RESOURCE_CONFLICIT=\u8D44\u6E90\u51B2\u7A81
NET_ERROR_TALK_UNSUPPORTED_ENCODE=\u4E0D\u652F\u6301\u7684\u8BED\u97F3\u7F16\u7801\u683C\u5F0F
NET_ERROR_TALK_RIGHTLESS=\u65E0\u6743\u9650
NET_ERROR_TALK_FAILED=\u8BF7\u6C42\u5BF9\u8BB2\u5931\u8D25
NET_ERROR_GET_MACHINE_CFG=\u83B7\u53D6\u673A\u5668\u76F8\u5173\u914D\u7F6E\u5931\u8D25
NET_ERROR_SET_MACHINE_CFG=\u8BBE\u7F6E\u673A\u5668\u76F8\u5173\u914D\u7F6E\u5931\u8D25
NET_ERROR_GET_DATA_FAILED=\u8BBE\u5907\u65E0\u6CD5\u83B7\u53D6\u5F53\u524D\u8BF7\u6C42\u6570\u636E
NET_ERROR_MAC_VALIDATE_FAILED=MAC\u5730\u5740\u9A8C\u8BC1\u5931\u8D25
NET_ERROR_GET_INSTANCE=\u83B7\u53D6\u670D\u52A1\u5668\u5B9E\u4F8B\u5931\u8D25
NET_ERROR_JSON_REQUEST=\u751F\u6210\u7684json\u5B57\u7B26\u4E32\u9519\u8BEF
NET_ERROR_JSON_RESPONSE=\u54CD\u5E94\u7684json\u5B57\u7B26\u4E32\u9519\u8BEF
NET_ERROR_VERSION_HIGHER=\u534F\u8BAE\u7248\u672C\u4F4E\u4E8E\u5F53\u524D\u4F7F\u7528\u7684\u7248\u672C
NET_SPARE_NO_CAPACITY=\u8BBE\u5907\u64CD\u4F5C\u5931\u8D25,\u5BB9\u91CF\u4E0D\u8DB3
NET_ERROR_SOURCE_IN_USE=\u663E\u793A\u6E90\u88AB\u5176\u4ED6\u8F93\u51FA\u5360\u7528
NET_ERROR_REAVE=\u9AD8\u7EA7\u7528\u6237\u62A2\u5360\u4F4E\u7EA7\u7528\u6237\u8D44\u6E90
NET_ERROR_NETFORBID=\u7981\u6B62\u5165\u7F51
NET_ERROR_GETCFG_MACFILTER=\u83B7\u53D6MAC\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_MACFILTER=\u8BBE\u7F6EMAC\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_GETCFG_IPMACFILTER=\u83B7\u53D6IP/MAC\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_SETCFG_IPMACFILTER=\u8BBE\u7F6EIP/MAC\u8FC7\u6EE4\u914D\u7F6E\u5931\u8D25
NET_ERROR_OPERATION_OVERTIME=\u5F53\u524D\u64CD\u4F5C\u8D85\u65F6
NET_ERROR_SENIOR_VALIDATE_FAILED=\u9AD8\u7EA7\u6821\u9A8C\u5931\u8D25
NET_ERROR_DEVICE_ID_NOT_EXIST=\u8BBE\u5907ID\u4E0D\u5B58\u5728
NET_ERROR_UNSUPPORTED=\u4E0D\u652F\u6301\u5F53\u524D\u64CD\u4F5C
NET_ERROR_PROXY_DLLLOAD=\u4EE3\u7406\u5E93\u52A0\u8F7D\u5931\u8D25
NET_ERROR_PROXY_ILLEGAL_PARAM=\u4EE3\u7406\u7528\u6237\u53C2\u6570\u4E0D\u5408\u6CD5
NET_ERROR_PROXY_INVALID_HANDLE=\u4EE3\u7406\u53E5\u67C4\u65E0\u6548
NET_ERROR_PROXY_LOGIN_DEVICE_ERROR=\u4EE3\u7406\u767B\u5165\u524D\u7AEF\u8BBE\u5907\u5931\u8D25
NET_ERROR_PROXY_START_SERVER_ERROR=\u542F\u52A8\u4EE3\u7406\u670D\u52A1\u5931\u8D25
NET_ERROR_SPEAK_FAILED=\u8BF7\u6C42\u558A\u8BDD\u5931\u8D25
NET_ERROR_NOT_SUPPORT_F6=\u8BBE\u5907\u4E0D\u652F\u6301\u6B64F6\u63A5\u53E3\u8C03\u7528
NET_ERROR_CD_UNREADY=\u5149\u76D8\u672A\u5C31\u7EEA
NET_ERROR_DIR_NOT_EXIST=\u76EE\u5F55\u4E0D\u5B58\u5728
NET_ERROR_UNSUPPORTED_SPLIT_MODE=\u8BBE\u5907\u4E0D\u652F\u6301\u7684\u5206\u5272\u6A21\u5F0F
NET_ERROR_OPEN_WND_PARAM=\u5F00\u7A97\u53C2\u6570\u4E0D\u5408\u6CD5
NET_ERROR_LIMITED_WND_COUNT=\u5F00\u7A97\u6570\u91CF\u8D85\u8FC7\u9650\u5236
NET_ERROR_UNMATCHED_REQUEST=\u8BF7\u6C42\u547D\u4EE4\u4E0E\u5F53\u524D\u6A21\u5F0F\u4E0D\u5339\u914D
NET_RENDER_ENABLELARGEPICADJUSTMENT_ERROR=Render\u5E93\u542F\u7528\u9AD8\u6E05\u56FE\u50CF\u5185\u90E8\u8C03\u6574\u7B56\u7565\u51FA\u9519
NET_ERROR_UPGRADE_FAILED=\u8BBE\u5907\u5347\u7EA7\u5931\u8D25
NET_ERROR_NO_TARGET_DEVICE=\u627E\u4E0D\u5230\u76EE\u6807\u8BBE\u5907
NET_ERROR_NO_VERIFY_DEVICE=\u627E\u4E0D\u5230\u9A8C\u8BC1\u8BBE\u5907
NET_ERROR_CASCADE_RIGHTLESS=\u65E0\u7EA7\u8054\u6743\u9650
NET_ERROR_LOW_PRIORITY=\u4F4E\u4F18\u5148\u7EA7
NET_ERROR_REMOTE_REQUEST_TIMEOUT=\u8FDC\u7A0B\u8BBE\u5907\u8BF7\u6C42\u8D85\u65F6
NET_ERROR_LIMITED_INPUT_SOURCE=\u8F93\u5165\u6E90\u8D85\u51FA\u6700\u5927\u8DEF\u6570\u9650\u5236
NET_ERROR_SET_LOG_PRINT_INFO=\u8BBE\u7F6E\u65E5\u5FD7\u6253\u5370\u5931\u8D25
NET_ERROR_PARAM_DWSIZE_ERROR=\u5165\u53C2\u7684dwsize\u5B57\u6BB5\u51FA\u9519
NET_ERROR_LIMITED_MONITORWALL_COUNT=\u7535\u89C6\u5899\u6570\u91CF\u8D85\u8FC7\u4E0A\u9650
NET_ERROR_PART_PROCESS_FAILED=\u90E8\u5206\u8FC7\u7A0B\u6267\u884C\u5931\u8D25
NET_ERROR_TARGET_NOT_SUPPORT=\u8BE5\u529F\u80FD\u4E0D\u652F\u6301\u8F6C\u53D1
NET_ERROR_VISITE_FILE=\u8BBF\u95EE\u6587\u4EF6\u5931\u8D25
NET_ERROR_DEVICE_STATUS_BUSY=\u8BBE\u5907\u5FD9
NET_USER_PWD_NOT_AUTHORIZED=\u4FEE\u6539\u5BC6\u7801\u65E0\u6743\u9650
NET_USER_PWD_NOT_STRONG=\u5BC6\u7801\u5F3A\u5EA6\u4E0D\u591F
NET_ERROR_NO_SUCH_CONFIG=\u6CA1\u6709\u5BF9\u5E94\u7684\u914D\u7F6E
NET_ERROR_AUDIO_RECORD_FAILED=\u5F55\u97F3\u5931\u8D25
NET_ERROR_SEND_DATA_FAILED=\u6570\u636E\u53D1\u9001\u5931\u8D25
NET_ERROR_OBSOLESCENT_INTERFACE=\u5E9F\u5F03\u63A5\u53E3
NET_ERROR_INSUFFICIENT_INTERAL_BUF=\u5185\u90E8\u7F13\u51B2\u4E0D\u8DB3
NET_ERROR_NEED_ENCRYPTION_PASSWORD=\u4FEE\u6539\u8BBE\u5907ip\u65F6,\u9700\u8981\u6821\u9A8C\u5BC6\u7801
NET_ERROR_NOSUPPORT_RECORD=\u8BBE\u5907\u4E0D\u652F\u6301\u6B64\u8BB0\u5F55\u96C6
NET_ERROR_SERIALIZE_ERROR=\u6570\u636E\u5E8F\u5217\u5316\u9519\u8BEF
NET_ERROR_DESERIALIZE_ERROR=\u6570\u636E\u53CD\u5E8F\u5217\u5316\u9519\u8BEF
NET_ERROR_LOWRATEWPAN_ID_EXISTED=\u8BE5\u65E0\u7EBFID\u5DF2\u5B58\u5728
NET_ERROR_LOWRATEWPAN_ID_LIMIT=\u65E0\u7EBFID\u6570\u91CF\u5DF2\u8D85\u9650
NET_ERROR_LOWRATEWPAN_ID_ABNORMAL=\u65E0\u7EBF\u5F02\u5E38\u6DFB\u52A0
NET_ERROR_ENCRYPT=\u52A0\u5BC6\u6570\u636E\u5931\u8D25
NET_ERROR_PWD_ILLEGAL=\u65B0\u5BC6\u7801\u4E0D\u5408\u89C4\u8303
NET_ERROR_DEVICE_ALREADY_INIT=\u8BBE\u5907\u5DF2\u7ECF\u521D\u59CB\u5316
NET_ERROR_SECURITY_CODE=\u5B89\u5168\u7801\u9519\u8BEF
NET_ERROR_SECURITY_CODE_TIMEOUT=\u5B89\u5168\u7801\u8D85\u51FA\u6709\u6548\u671F
NET_ERROR_GET_PWD_SPECI=\u83B7\u53D6\u5BC6\u7801\u89C4\u8303\u5931\u8D25
NET_ERROR_NO_AUTHORITY_OF_OPERATION=\u65E0\u6743\u9650\u8FDB\u884C\u8BE5\u64CD\u4F5C
NET_ERROR_DECRYPT=\u89E3\u5BC6\u6570\u636E\u5931\u8D25
NET_ERROR_2D_CODE=2Dcode\u6821\u9A8C\u5931\u8D25
NET_ERROR_INVALID_REQUEST=\u975E\u6CD5\u7684RPC\u8BF7\u6C42
NET_ERROR_PWD_RESET_DISABLE=\u5BC6\u7801\u91CD\u7F6E\u529F\u80FD\u5DF2\u5173\u95ED
NET_ERROR_PLAY_PRIVATE_DATA=\u663E\u793A\u79C1\u6709\u6570\u636E\uFF0C\u6BD4\u5982\u89C4\u5219\u6846\u7B49\u5931\u8D25
NET_ERROR_ROBOT_OPERATE_FAILED=\u673A\u5668\u4EBA\u64CD\u4F5C\u5931\u8D25
NET_ERROR_PHOTOSIZE_EXCEEDSLIMIT=\u56FE\u7247\u5927\u5C0F\u8D85\u9650
NET_ERROR_USERID_INVALID=\u7528\u6237ID\u4E0D\u5B58\u5728
NET_ERROR_EXTRACTFEATURE_FAILED=\u7167\u7247\u7279\u5F81\u503C\u63D0\u53D6\u5931\u8D25
NET_ERROR_PHOTO_EXIST=\u7167\u7247\u5DF2\u5B58\u5728
NET_ERROR_PHOTO_OVERFLOW=\u7167\u7247\u6570\u91CF\u8D85\u8FC7\u4E0A\u9650
NET_ERROR_CHANNEL_ALREADY_OPENED=\u901A\u9053\u5DF2\u7ECF\u6253\u5F00
NET_ERROR_CREATE_SOCKET=\u521B\u5EFA\u5957\u63A5\u5B57\u5931\u8D25
NET_ERROR_CHANNEL_NUM=\u901A\u9053\u53F7\u9519\u8BEF
NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_ID_EXCEED=\u7EC4ID\u8D85\u8FC7\u6700\u5927\u503C

# RealPlay Info
START_REALPLAY=\u5F00\u59CB\u9884\u89C8
STOP_REALPLAY=\u505C\u6B62\u9884\u89C8
ATTACH=\u667A\u80FD\u8BA2\u9605
DETACH=\u53D6\u6D88\u8BA2\u9605
REALPLAY_SUCCEED=\u5B9E\u65F6\u9884\u89C8\u6210\u529F
REALPLAY_FAILED=\u5B9E\u65F6\u9884\u89C8\u5931\u8D25

CHN=\u901A\u9053
CHANNEL=\u901A\u9053
STREAM_TYPE=\u7801\u6D41
MASTER_AND_SUB_STREAM=\u4E3B\u8F85\u7801\u6D41
MASTER_STREAM=\u4E3B\u7801\u6D41
SUB_STREAM=\u8F85\u7801\u6D41

# Capture Picture
LOCAL_CAPTURE=\u672C\u5730\u6293\u56FE
REMOTE_CAPTURE=\u8FDC\u7A0B\u6293\u56FE
TIMER_CAPTURE=\u5B9A\u65F6\u6293\u56FE
STOP_CAPTURE=\u505C\u6B62\u6293\u56FE
INTERVAL=\u95F4\u9694

TIME_INTERVAL_ILLEGAL=\u65F6\u95F4\u95F4\u9694\u8F93\u5165\u975E\u6CD5 
PLEASE_START_REALPLAY=\u8BF7\u5148\u6253\u5F00\u5B9E\u65F6\u9884\u89C8

# PTZ Info 
PTZ_CONTROL=\u4E91\u53F0\u63A7\u5236
LEFT_UP=\u5DE6\u4E0A
UP=\u4E0A
RIGHT_UP=\u53F3\u4E0A
LEFT=\u5DE6
RIGHT=\u53F3
LEFT_DOWN=\u5DE6\u4E0B
DOWN=\u4E0B
RIGHT_DOWN=\u53F3\u4E0B

ZOOM_ADD=\u53D8\u500D+
ZOOM_DEC=\u53D8\u500D-
FOCUS_ADD=\u8C03\u7126+
FOCUS_DEC=\u8C03\u7126-
IRIS_ADD=\u5149\u5708+
IRIS_DEC=\u5149\u5708-

SPEED=\u901F\u7387

#ITS
EVENT_INFO=\u4E8B\u4EF6\u4FE1\u606F
EVENT_NAME=\u4E8B\u4EF6\u540D\u79F0
EVENT_TIME=\u4E8B\u4EF6\u65F6\u95F4
EVENT_PICTURE=\u4E8B\u4EF6\u4EE5\u53CA\u56FE\u7247
PLATE_PICTURE=\u8F66\u724C\u56FE
LICENSE_PLATE=\u8F66\u724C\u53F7
PLATE_TYPE=\u8F66\u724C\u7C7B\u578B
PLATE_COLOR=\u8F66\u724C\u989C\u8272
VEHICLE_TYPE=\u8F66\u8F86\u7C7B\u578B
VEHICLE_SIZE=\u8F66\u8F86\u5927\u5C0F
VEHICLE_COLOR=\u8F66\u8F86\u989C\u8272
FILE_COUNT=\u6587\u4EF6\u603B\u6570
FILE_INDEX=\u6587\u4EF6\u7F16\u53F7
GROUP_ID=\u7EC4ID
ILLEGAL_PLACE=\u8FDD\u6CD5\u5730\u70B9
LANE_NUMBER=\u8F66\u9053\u53F7
MANUAL_CAPTURE=\u624B\u52A8\u6293\u62CD
OPEN_STROBE=\u51FA\u5165\u53E3\u5F00\u95F8
CLOSE_STROBE=\u51FA\u5165\u53E3\u5173\u95F8
INDEX=\u5E8F\u53F7

OPERATE=\u64CD\u4F5C
FUNCTION=\u57FA\u672C

UNDEFINED_COLOR=\u672A\u5B9A\u4E49\u989C\u8272
BLACK=\u9ED1\u8272
WHITE=\u767D\u8272
RED=\u7EA2\u8272
BLUE=\u84DD\u8272
GREEN=\u7EFF\u8272
YELLOW=\u9EC4\u8272
GRAY=\u7070\u8272
ORANGE=\u6A59\u8272

LIGHT_DUTY=\u5C0F\u578B\u8F66
MEDIUM=\u4E2D\u578B\u8F66
OVER_SIZE=\u5927\u578B\u8F66
MINI_SIZE=\u5FAE\u578B\u8F66
LARGE_SIZE=\u957F\u8F66

NO_PLATENUMBER=\u65E0\u8F66\u724C
MANUALSNAP_SUCCEED=\u624B\u52A8\u6293\u62CD\u6210\u529F
MANUALSNAP_FAILED=\u624B\u52A8\u6293\u62CD\u5931\u8D25

OPEN_STROBE_SUCCEED=\u5F00\u95F8\u6210\u529F
OPEN_STROBE_FAILED=\u5F00\u95F8\u5931\u8D25
CLOSE_STROBE_SUCCEED=\u5173\u95F8\u6210\u529F
CLOSE_STROBE_FAILED=\u5173\u95F8\u5931\u8D25

EVENT_IVS_TRAFFICJUNCTION=\u4EA4\u901A\u5361\u53E3
EVENT_IVS_TRAFFIC_RUNREDLIGHT=\u95EF\u7EA2\u706F
EVENT_IVS_TRAFFIC_OVERLINE=\u538B\u8F66\u9053\u7EBF
EVENT_IVS_TRAFFIC_RETROGRADE=\u9006\u884C
EVENT_IVS_TRAFFIC_TURNLEFT=\u8FDD\u7AE0\u5DE6\u8F6C
EVENT_IVS_TRAFFIC_TURNRIGHT=\u8FDD\u7AE0\u53F3\u8F6C
EVENT_IVS_TRAFFIC_UTURN=\u8FDD\u7AE0\u6389\u5934
EVENT_IVS_TRAFFIC_OVERSPEED=\u8D85\u901F
EVENT_IVS_TRAFFIC_UNDERSPEED=\u4F4E\u901F
EVENT_IVS_TRAFFIC_PARKING=\u8FDD\u7AE0\u505C\u8F66
EVENT_IVS_TRAFFIC_WRONGROUTE=\u4E0D\u6309\u8F66\u9053\u884C\u9A76
EVENT_IVS_TRAFFIC_CROSSLANE=\u8FDD\u7AE0\u53D8\u9053
EVENT_IVS_TRAFFIC_OVERYELLOWLINE=\u538B\u9EC4\u7EBF
EVENT_IVS_TRAFFIC_YELLOWPLATEINLANE=\u9EC4\u724C\u8F66\u5360\u9053
EVENT_IVS_TRAFFIC_PEDESTRAINPRIORITY=\u6591\u9A6C\u7EBF\u884C\u4EBA\u4F18\u5148
EVENT_IVS_TRAFFIC_MANUALSNAP=\u4EA4\u901A\u624B\u52A8\u6293\u62CD
EVENT_IVS_TRAFFIC_VEHICLEINROUTE=\u6709\u8F66\u5360\u9053
EVENT_IVS_TRAFFIC_VEHICLEINBUSROUTE=\u5360\u7528\u516C\u4EA4\u8F66\u9053
EVENT_IVS_TRAFFIC_BACKING=\u8FDD\u7AE0\u5012\u8F66
EVENT_IVS_TRAFFIC_PARKINGSPACEPARKING=\u8F66\u4F4D\u6709\u8F66
EVENT_IVS_TRAFFIC_PARKINGSPACENOPARKING=\u8F66\u4F4D\u65E0\u8F66
EVENT_IVS_TRAFFIC_WITHOUT_SAFEBELT=\u4EA4\u901A\u672A\u7CFB\u5B89\u5168\u5E26

# DownLoad Info
DOWNLOAD_RECORD_BYTIME=\u6309\u65F6\u95F4\u4E0B\u8F7D
DOWNLOAD_RECORD_BYFILE=\u6309\u6587\u4EF6\u4E0B\u8F7D
QUERY=\u67E5\u8BE2 
DOWNLOAD=\u4E0B\u8F7D
STOP_DOWNLOAD=\u505C\u6B62\u4E0B\u8F7D
START_TIME=\u5F00\u59CB\u65F6\u95F4
END_TIME=\u7ED3\u675F\u65F6\u95F4
RECORD_TYPE=\u5F55\u50CF\u7C7B\u578B

GENERAL_RECORD=\u666E\u901A\u5F55\u50CF
ALARM_RECORD=\u62A5\u8B66\u5F55\u50CF
MOTION_DETECTION=\u79FB\u52A8\u68C0\u6D4B\u5F55\u50CF
CARD_NUMBER_RECORD=\u5361\u53F7\u5F55\u50CF
INTELLIGENT_DETECTION=\u667a\u80fd\u5f55\u50cf
POS_RECORD=\u0050\u004f\u0053\u5f55\u50cf

QUERY_RECORD_IS_NOT_EXIST=\u67E5\u8BE2\u5F55\u50CF\u4E0D\u5B58\u5728
PLEASE_CHECK_RECORD_TIME=\u8BF7\u68C0\u67E5\u5F55\u50CF\u65F6\u95F4
PLEASE_SELECT_TIME_AGAIN=\u8BF7\u91CD\u65B0\u9009\u62E9\u65F6\u95F4\uFF0C\u6700\u5927\u65F6\u95F4\u5DEE6\u5C0F\u65F6
DOWNLOAD_COMPLETED=\u4E0B\u8F7D\u7ED3\u675F

PLEASE_FIRST_QUERY_RECORD=\u8BF7\u5148\u67E5\u8BE2\u5F55\u50CF
PLEASE_FIRST_SELECT_ROW_WITH_DATA=\u8BF7\u5148\u9009\u62E9\u6709\u5F55\u50CF\u7684\u4E00\u884C

# Time Set
YEAR=\u5E74
MONTH=\u6708
DAY=\u65E5
HOUR=\u65F6
MINUTE=\u5206
SECOND=\u79D2
CONFIRM=\u786E\u5B9A
CANCEL=\u53D6\u6D88
DATE_CHOOSER=\u65F6\u95F4\u9009\u62E9

MONDAY=\u661F\u671F\u4E00
TUESDAY=\u661F\u671F\u4E8C
WEDNESDAY=\u661F\u671F\u4E09
THURSDAY=\u661F\u671F\u56DB
FRIDAY=\u661F\u671F\u4E94
SATURDAY=\u661F\u671F\u516D
SUNDAY=\u661F\u671F\u65E5

# Talk
TRANSMIT_TYPE=\u8F6C\u53D1\u7C7B\u578B
LOCAL_TRANSMIT_TYPE=\u672C\u5730\uFF08\u4E0D\u8F6C\u53D1\uFF09
REMOTE_TRANSMIT_TYPE=\u8FDC\u7A0B\uFF08\u8F6C\u53D1\uFF09
TRANSMIT_CHANNEL=\u8F6C\u53D1\u901A\u9053
START_TALK=\u5F00\u59CB\u901A\u8BDD
STOP_TALK=\u7ED3\u675F\u901A\u8BDD
TALK_FAILED=\u901A\u8BDD\u5931\u8D25

# DeviceSearchAndInt
DEVICESEARCH_OPERATE=\u8BBE\u5907\u641C\u7D22\u64CD\u4F5C
DEVICESEARCH_RESULT=\u8BBE\u5907\u641C\u7D22\u7ED3\u679C
DEVICEINIT=\u8BBE\u5907\u521D\u59CB\u5316

DEVICESEARCH=\u8BBE\u5907\u641C\u7D22
DEVICE_POINT_TO_POINT_SEARCH=\u8BBE\u5907IP\u70B9\u5BF9\u70B9\u641C\u7D22

START_SEARCH=\u5F00\u59CB\u641C\u7D22
STOP_SEARCH=\u505C\u6B62\u641C\u7D22

START_IP=\u8D77\u59CBIP
END_IP=\u7ED3\u675FIP

DEVICE_TYPE=\u8BBE\u5907\u7C7B\u578B
MAC=MAC
SN=\u5E8F\u5217\u53F7
DEVICE_INIT_STATE=\u521D\u59CB\u5316\u72B6\u6001
INIT_PASSWD=\u521D\u59CB\u5316\u5BC6\u7801
PHONE=\u624B\u673A\u53F7
MAIL=\u90AE\u7BB1
IP_VERSION=IP\u7248\u672C
SUB_MASK=\u5B50\u7F51\u63A9\u7801
GETWAY=\u7F51\u5173
DETAIL_TYPE=\u8BE6\u7EC6\u7C7B\u578B
HTTP_PORT=HTTP\u7AEF\u53E3\u53F7
LOCAL_IP=\u672c\u5730ip
CONFIRM_PASSWORD=\u786E\u8BA4\u5BC6\u7801

OLD_DEVICE=\u8001\u8BBE\u5907
DONOT_SUPPORT_INITIALIZATION=\u4E0D\u652F\u6301\u521D\u59CB\u5316
NOT_INITIALIZED=\u672A\u521D\u59CB\u5316
INITIALIZED=\u5DF2\u521D\u59CB\u5316

THE_IP_CONTROL_SCOPE=\u8303\u56F4\u592A\u5927\uFF0C\u8BF7\u63A7\u5236\u57281000\u4E2A\u4E4B\u95F4
PLEASE_FIRST_SELECT_INITIALIZED_DEVICE=\u8BF7\u5148\u9009\u62E9\u9700\u8981\u521D\u59CB\u5316\u7684\u8BBE\u5907
PLEASE_INPUT_PHONE=\u8BF7\u8F93\u5165\u624B\u673A\u53F7
PLEASE_INPUT_MAIL=\u8BF7\u8F93\u5165\u90AE\u7BB1
INCONSISTENT=\u5BC6\u7801\u548C\u786E\u8BA4\u5BC6\u7801\u4E0D\u4E00\u81F4
PLEASE_CHECK_IP=\u8BF7\u68C0\u67E5IP

SEARCHING_WAITING=\u6B63\u5728\u641C\u7D22\uFF0C\u8BF7\u7B49\u5F85\u641C\u7D22\u7ED3\u675F

START_LISTEN=\u5F00\u59CB\u76D1\u542C
STOP_LISTEN=\u505C\u6B62\u76D1\u542C
SHOW_ALARM_EVENT=\u62A5\u8B66\u4E8B\u4EF6\u7ED3\u679C\u4FE1\u606F
ALARM_LISTEN_FAILED=\u62A5\u8B66\u76D1\u542C\u5931\u8D25
ALARM_MESSAGE=\u62A5\u8B66\u4FE1\u606F

EXTERNAL_ALARM=\u5916\u90E8\u62A5\u8B66
MOTION_ALARM=\u52A8\u6001\u68C0\u6D4B\u62A5\u8B66
VIDEOLOST_ALARM=\u89C6\u9891\u4E22\u5931\u62A5\u8B66
SHELTER_ALARM=\u89C6\u9891\u906E\u6321\u62A5\u8B66
DISKFULL_ALARM=\u786C\u76D8\u6EE1\u62A5\u8B66
DISKERROR_ALARM=\u574F\u786C\u76D8\u62A5\u8B66
START=\u5F00\u59CB
STOP=\u7ED3\u675F

CURRENT_TIME=\u5F53\u524D\u65F6\u95F4
DEVICE_REBOOT=\u8BBE\u5907\u91CD\u542F
SYNCHRONIZE_TIME=\u65F6\u95F4\u540C\u6B65
REBOOT=\u91CD\u542F
SET_TIME=\u8BBE\u7F6E\u65F6\u95F4
GET_TIME=\u83B7\u53D6\u65F6\u95F4
REBOOT_TIPS=\u60A8\u786E\u5B9A\u8981\u91CD\u542F\u5417\uFF1F
OPERATE_SUCCESS=\u64CD\u4F5C\u6210\u529F

#FaceRecognition
FACE_GROUP_ID=\u4EBA\u8138\u5E93ID
FACE_GROUP_NAME=\u4EBA\u8138\u5E93\u540D\u79F0
PERSON_COUNT=\u4EBA\u5458\u4E2A\u6570

GROUP_OPERATE=\u4EBA\u8138\u5E93\u64CD\u4F5C
PERSON_OPERATE=\u4EBA\u5458\u64CD\u4F5C

FACE_RECOGNITION_EVENT=\u4EBA\u8138\u8BC6\u522B\u4E8B\u4EF6
FACE_DETECT_EVENT=\u4EBA\u8138\u68C0\u6D4B\u4E8B\u4EF6

GLOBAL_PICTURE=\u5168\u666F\u56FE
PERSON_PICTURE=\u4EBA\u8138\u56FE
CANDIDATE_PICTURE=\u5019\u9009\u4EBA\u56FE

TIME=\u65F6\u95F4
SEX=\u6027\u522B
AGE=\u5E74\u9F84
COLOR=\u80a4\u8272
EYE=\u773C\u775B
MOUTH=\u5634\u5DF4
MASK=\u53E3\u7F69
BEARD=\u80E1\u5B50
MALE=\u7537
FEMALE=\u5973
ID_CARD=\u8EAB\u4EFD\u8BC1
OFFICE_CARD=\u519b\u5b98\u8bc1
PASSPORT=\u62A4\u7167
UNIDENTIFIED=\u672A\u8BC6\u522B
HAVE_BEARD=\u6709\u80E1\u5B50
NO_BEARD=\u6CA1\u80E1\u5B50
OPEN_MOUTH=\u5F20\u5634
CLOSE_MOUTH=\u95ED\u5634
YELLOW_COLOR=\u9ec4\u80a4\u8272
BLACK_COLOR=\u9ed1\u80a4\u8272
WHITE_COLOR=\u767d\u80a4\u8272
OPEN_EYE=\u7741\u773C
CLOSE_EYE=\u95ED\u773C
SMILE=\u5FAE\u7B11
ANGER=\u6124\u6012
SADNESS=\u60B2\u4F24
DISGUST=\u538C\u6076
FEAR=\u5BB3\u6015
SURPRISE=\u60CA\u8BB6
NEUTRAL=\u6B63\u5E38
LAUGH=\u5927\u7B11
WEAR_MASK=\u6234\u53E3\u7F69
NO_MASK=\u6CA1\u6234\u53E3\u7F69
WEAR_GLASSES=\u6234\u773C\u955C
NO_GLASSES=\u4E0D\u6234\u773C\u955C
UNKNOW=\u672A\u77E5
UNLIMITED=\u4E0D\u9650

NAME=\u59D3\u540D
BIRTHDAY=\u751F\u65E5
ID_NO=\u8BC1\u4EF6\u53F7
ID_TYPE=\u8BC1\u4EF6\u7C7B\u578B
SIMILARITY=\u76F8\u4F3C\u5EA6
UID=\u4EBA\u5458\u6807\u8BC6\u7B26
STRANGER=\u964C\u751F\u4EBA
GLASSES=\u773c\u955c
PICTURE_PATH=\u56fe\u7247\u8def\u5f84
FACE_LIBRARY_ID=\u4eba\u8138\u5e93ID
FACE_LIBRARY_NAME=\u4eba\u8138\u5e93\u540d\u79f0


ADD=\u6DFB\u52A0
MODIFY=\u4FEE\u6539
DELETE=\u5220\u9664
FRESH=\u5237\u65B0
ADD_GROUP=\u6DFB\u52A0\u4EBA\u8138\u5E93
MODIFY_GROUP=\u4FEE\u6539\u4EBA\u8138\u5E93
DEL_GROUP=\u5220\u9664\u4EBA\u8138\u5E93
DISPOSITION=\u5E03\u63A7
DEL_DISPOSITION=\u64A4\u63A7

FIND_CONDITION=\u67E5\u627E\u6761\u4EF6
FIND_PERSON=\u67E5\u627E\u4EBA\u5458
ADD_PERSON=\u6DFB\u52A0\u4EBA\u5458
MODIFY_PERSON=\u4FEE\u6539\u4EBA\u5458
DEL_PERSON=\u5220\u9664\u4EBA\u5458

PREVIOUSPAGE=\u4E0A\u4E00\u9875
LASTPAGE=\u4E0B\u4E00\u9875
SELECT_PICTURE=\u9009\u62E9\u56FE\u7247
SEARCH_BY_PIC=\u4ee5\u56fe\u641c\u56fe
DOWNLOAD_QUERY_PICTURE=\u4e0b\u8f7d\u67e5\u8be2\u5230\u7684\u56fe\u7247
FACE_LIBRARY=\u4eba\u8138\u5e93
HISTORY_LIBRARY=\u5386\u53f2\u5e93
CHOOSE_FACE_PIC=\u8bf7\u9009\u62e9\u4eba\u8138\u56fe\u7247
EVENT_TYPE=\u4E8B\u4EF6\u7C7B\u578B
PAGES_NUMBER=\u9875\u6570

SIMILARITY_RANGE=\u76F8\u4F3C\u5EA6\u8303\u56F4[0, 100]
PLEASE_INPUT_GROUPNAME=\u8BF7\u8F93\u5165\u4EBA\u8138\u5E93\u540D\u79F0
PLEASE_SELECT_GROUP=\u8BF7\u9009\u62E9\u4EBA\u8138\u5E93

PLEASE_SELECT_PERSON=\u8BF7\u9009\u62E9\u4EBA\u5458
PLEASE_ADD_DISPOSITION_INFO=\u8BF7\u6DFB\u52A0\u5E03\u63A7\u4FE1\u606F
PLEASE_SELECT_DEL_DISPOSITION_INFO=\u8BF7\u9009\u62E9\u64A4\u63A7\u4FE1\u606F

#AutoRegister
AUTOREGISTER_LISTEN=\u4E3B\u52A8\u6CE8\u518C\u76D1\u542C
DEVICE_CONFIG=\u8BBE\u5907\u914D\u7F6E
DEVICE_LIST=\u8BBE\u5907\u5217\u8868
DEVICE_MANAGER=\u8BBE\u5907\u7BA1\u7406
ADD_DEVICE=\u6DFB\u52A0\u8BBE\u5907
MODIFY_DEVICE=\u4FEE\u6539\u8BBE\u5907
DELETE_DEVICE=\u5220\u9664\u8BBE\u5907
CLEAR_DEVICE=\u6E05\u7A7A\u8BBE\u5907
IMPORT_DEVICE=\u5BFC\u5165\u8BBE\u5907
EXPORT_DEVICE=\u5BFC\u51FA\u8BBE\u5907
DEVICE_ID=\u8BBE\u5907ID
ENABLE=\u542F\u7528
REGISTER_ADDRESS=\u6CE8\u518C\u5730\u5740
REGISTER_PORT=\u6CE8\u518C\u7AEF\u53E3\u53F7
GET=\u83B7\u53D6
SET=\u8BBE\u7F6E
RECORD=\u5F55\u97F3
DEVICE_LOGIN=\u8BBE\u5907\u5DF2\u767B\u5F55

ALREADY_EXISTED=\u5DF2\u5B58\u5728
ALREADY_EXISTED_WHETHER_OR_NOT_TO_COVER=\u5DF2\u5B58\u5728\uFF0C\u662F\u5426\u8986\u76D6(\u5426\uFF0C\u4E0D\u4F1A\u5BFC\u51FA)
FILE_OPEN_PLEASE_CLOSE_FILE=\u6587\u4EF6\u88AB\u6253\u5F00\uFF0C\u8BF7\u5148\u5173\u95ED\u6587\u4EF6
IMPORT_COMPLETION=\u5BFC\u5165\u5B8C\u6210
EXPORT_COMPLETION=\u5BFC\u51FA\u5B8C\u6210
FILE_NOT_EXIST=\u6587\u4EF6\u4E0D\u5B58\u5728

PLEASE_INPUT=\u8BF7\u8F93\u5165
MAX_SUPPORT_100=\u6700\u5927\u652F\u6301100\u4E2A

#Attendance
ATTENDANCE=\u8003\u52E4\u673A
USER_ID=\u7528\u6237ID
USER_NAME=\u7528\u6237\u540D
CARD_NO=\u5361\u53F7
QUERY_CONDITION=\u67E5\u8BE2\u6761\u4EF6
USER_OPERATE=\u7528\u6237\u64CD\u4F5C
FINGERPRINT_OPERATE=\u6307\u7EB9\u64CD\u4F5C
OPERATE_BY_USER_ID=\u901A\u8FC7\u7528\u6237ID\u64CD\u4F5C\u6307\u7EB9
OPERATE_BY_FINGERPRINT_ID=\u901A\u8FC7\u6307\u7EB9ID\u64CD\u4F5C\u6307\u7EB9
FINGERPRINT_ID=\u6307\u7EB9ID
SEARCH=\u67E5\u627E
SEARCH_FINGERPRINT=\u67E5\u627E\u6307\u7EB9
ADD_FINGERPRINT=\u6DFB\u52A0\u6307\u7EB9
DELETE_FINGERPRINT=\u5220\u9664\u6307\u7EB9
SUBSCRIBE=\u8BA2\u9605
UNSUBSCRIBE=\u53D6\u6D88\u8BA2\u9605
USER_LIST=\u7528\u6237\u5217\u8868
NEXT_PAGE=\u4E0B\u4E00\u9875
USER_INFO=\u7528\u6237\u4FE1\u606F
DOOROPEN_METHOD=\u5F00\u95E8\u65B9\u5F0F
FINGERPRINT=\u6307\u7EB9
FINGERPRINT_INFO=\u6307\u7EB9\u4FE1\u606F
FINGERPRINT_DATA=\u6307\u7EB9\u6570\u636E
CARD=\u5361
DELETE_FINGERPRINT_PROMPT=\u5C06\u4F1A\u5220\u9664\u8BE5\u7528\u6237\u4E0B\u6240\u6709\u6307\u7EB9\u6570\u636E
SUBSCRIBE_FAILED=\u8BA2\u9605\u5931\u8D25
FINGERPRINT_ID_ILLEGAL=\u6307\u7EB9ID\u975E\u6CD5
FINGERPRINT_COLLECTION=\u6307\u7EB9\u91C7\u96C6
START_COLLECTION=\u5F00\u59CB\u91C7\u96C6
STOP_COLLECTION=\u505C\u6B62\u91C7\u96C6
IN_THE_COLLECTION=\u91C7\u96C6\u4E2D...
COLLECTION_COMPLETED=\u91C7\u96C6\u5B8C\u6210
COLLECTION_FAILED=\u91C7\u96C6\u5931\u8D25
FINGERPRINT_ID_NOT_EXIST=\u6307\u7EB9ID\u4E0D\u5B58\u5728
USER_ID_EXCEED_LENGTH=\u7528\u6237ID\u8D85\u8FC7\u6700\u5927\u957F\u5EA6
USER_NAME_EXCEED_LENGTH=\u7528\u6237\u540D\u8D85\u8FC7\u6700\u5927\u957F\u5EA6
CARD_NO_EXCEED_LENGTH=\u5361\u53F7\u8D85\u8FC7\u6700\u5927\u957F\u5EA6
CARD_NAME_EXCEED_LENGTH=\u5361\u540D\u8D85\u8FC7\u6700\u5927\u9650\u5236
CARD_PASSWD_EXCEED_LENGTH=\u5361\u5BC6\u7801\u8D85\u8FC7\u6700\u5927\u9650\u5236

#Gate

GATE=\u4eba\u8138\u5f00\u95e8
CARD_OPERATE=\u5361\u64CD\u4F5C
CARD_INFO=\u5361\u4FE1\u606F
CARD_MANAGER=\u5361\u7BA1\u7406

CLEAR=\u6E05\u7A7A

OPEN_STATUS=\u5F00\u95E8\u72B6\u6001
OPEN_METHOD=\u5F00\u95E8\u65B9\u5F0F

TEMPERATURE=\u6e29\u5ea6
MASK_STATUS=\u53e3\u7f69\u72b6\u6001

CARD_UNKNOW=\u672A\u77E5\u5361
CARD_GENERAL=\u4E00\u822C\u5361
CARD_VIP=VIP\u5361
CARD_GUEST=\u6765\u5BBE\u5361
CARD_PATROL=\u5DE1\u903B\u5361
CARD_BACKLIST=\u9ED1\u540D\u5355\u5361
CARD_COERCE=\u80C1\u8FEB\u5361
CARD_POLLING=\u5DE1\u68C0\u5361
CARD_MOTHERCARD=\u6BCD\u5361

STATE_UNKNOWN=\u672A\u77E5
STATE_NORMAL=\u6B63\u5E38
STATE_LOSE=\u4E22\u5931
STATE_LOGOFF=\u6CE8\u9500
STATE_FREEZE=\u51BB\u7ED3
STATE_ARREARS=\u6B20\u8D39
STATE_OVERDUE=\u903E\u671F
STATE_PREARREARS=\u9884\u6B20\u8D39

RECORD_NO=\u8BB0\u5F55\u96C6\u7F16\u53F7
CARD_NAME=\u5361\u540D
CARD_STATUS=\u5361\u72B6\u6001
CARD_PASSWORD=\u5361\u5BC6\u7801
CARD_TYPE=\u5361\u7C7B\u578B
CARD_NUM=\u8bc1\u4ef6\u53f7
USE_TIMES=\u4F7F\u7528\u6B21\u6570
IS_FIRST_ENTER=\u662F\u5426\u9996\u5361
IS_VALID=\u662F\u5426\u6709\u6548
VALID_PERIOD=\u6709\u6548\u671F
VALID_START_TIME=\u6709\u6548\u5F00\u59CB\u65F6\u95F4
VALID_END_TIME=\u6709\u6548\u7ED3\u675F\u65F6\u95F4
FIRST_ENTER=\u9996\u5361
NO_FIRST_ENTER=\u975E\u9996\u5361
VALID=\u6709\u6548
INVALID=\u65E0\u6548

PLEASE_SELECT_CARD=\u8BF7\u9009\u62E9\u5361
PLEASE_INPUT_CARDNO=\u8BF7\u8F93\u5165\u5361\u53F7
PLEASE_INPUT_USERID=\u8BF7\u8F93\u5165\u7528\u6237ID
WANT_CLEAR_ALL_INFO=\u4F60\u662F\u5426\u60F3\u8981\u6E05\u7A7A\u6240\u6709\u4FE1\u606F?

ADD_CARD_INDO_FAILED=\u6DFB\u52A0\u5361\u4FE1\u606F\u5931\u8D25 
ADD_CARD_INFO_AND_PERSON_PICTURE_SUCCEED=\u6DFB\u52A0\u5361\u4FE1\u606F\u548C\u4EBA\u8138\u6210\u529F
ADD_CARD_INFO_SUCCEED_BUT_ADD_PERSON_PICTURE_FAILED=\u6DFB\u52A0\u5361\u4FE1\u606F\u6210\u529F\uFF0C\u4F46\u6DFB\u52A0\u4EBA\u8138\u5931\u8D25
CARD_EXISTED_ADD_PERSON_PICTURE_SUCCEED=\u5361\u4FE1\u606F\u5DF2\u5B58\u5728\uFF0C\u6DFB\u52A0\u4EBA\u8138\u6210\u529F

MODIFY_CARD_INFO_SUCCEED=\u4FEE\u6539\u5361\u4FE1\u606F\u6210\u529F
MODIFY_CARD_INFO_FAILED=\u4FEE\u6539\u5361\u4FE1\u606F\u5931\u8D25
MODIFY_CARD_INFO_AND_PERSON_PICTURE_SUCCEED=\u4FEE\u6539\u5361\u4FE1\u606F\u548C\u4EBA\u8138\u6210\u529F
MODIFY_CARD_INFO_SUCCEED_BUT_MODIFY_PERSON_PICTURE_FAILED=\u4FEE\u6539\u5361\u4FE1\u606F\u6210\u529F\uFF0C\u4F46\u4FEE\u6539\u4EBA\u8138\u5931\u8D25

NET_ACCESS_DOOROPEN_METHOD_UNKNOWN=\u672A\u77E5
NET_ACCESS_DOOROPEN_METHOD_PWD_ONLY=\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD=\u5237\u5361\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_FIRST=\u5148\u5237\u5361\u540E\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_PWD_FIRST=\u5148\u5BC6\u7801\u540E\u5237\u5361\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_REMOTE=\u8FDC\u7A0B\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_BUTTON=\u5F00\u9501\u6309\u94AE\u8FDB\u884C\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT=\u6307\u7EB9\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_PWD_CARD_FINGERPRINT=\u5BC6\u7801+\u5237\u5361+\u6307\u7EB9\u7EC4\u5408\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_PWD_FINGERPRINT=\u5BC6\u7801+\u6307\u7EB9\u7EC4\u5408\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_FINGERPRINT=\u5237\u5361+\u6307\u7EB9\u7EC4\u5408\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_PERSONS=\u591A\u4EBA\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_KEY=\u94A5\u5319\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_COERCE_PWD=\u80C1\u8FEB\u5BC6\u7801\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_QRCODE=\u4E8C\u7EF4\u7801\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_FACE_RECOGNITION=\u4EBA\u8138\u8BC6\u522B\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD=\u4EBA\u8BC1\u5BF9\u6BD4
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD_AND_IDCARD=\u8EAB\u4EFD\u8BC1+ \u4EBA\u8BC1\u6BD4\u5BF9
NET_ACCESS_DOOROPEN_METHOD_BLUETOOTH=\u84DD\u7259\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_CUSTOM_PASSWORD=\u4E2A\u6027\u5316\u5BC6\u7801\u5F00\u95E8
NET_ACCESS_DOOROPEN_METHOD_USERID_AND_PWD=UserID+\u5BC6\u7801
NET_ACCESS_DOOROPEN_METHOD_FACE_AND_PWD=\u4EBA\u8138+\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_PWD=\u6307\u7EB9+\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_FACE=\u6307\u7EB9+\u4EBA\u8138\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FACE=\u5237\u5361+\u4EBA\u8138\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FACE_OR_PWD=\u4EBA\u8138\u6216\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_PWD=\u6307\u7EB9\u6216\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_FACE=\u6307\u7EB9\u6216\u4EBA\u8138\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FACE=\u5237\u5361\u6216\u4EBA\u8138\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT=\u5237\u5361\u6216\u6307\u7EB9\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_FACE_AND_PWD=\u6307\u7EB9+\u4EBA\u8138+\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FACE_AND_PWD=\u5237\u5361+\u4EBA\u8138+\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FINGERPRINT_AND_PWD=\u5237\u5361+\u6307\u7EB9+\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_PWD_AND_FACE=\u5361+\u6307\u7EB9+\u4EBA\u8138\u7EC4\u5408\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_FACE_OR_PWD=\u6307\u7EB9\u6216\u4EBA\u8138\u6216\u5BC6\u7801
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FACE_OR_PWD =\u5361\u6216\u4EBA\u8138\u6216\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT_OR_FACE=\u5361\u6216\u6307\u7EB9\u6216\u4EBA\u8138\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FINGERPRINT_AND_FACE_AND_PWD=\u5361+\u6307\u7EB9+\u4EBA\u8138+\u5BC6\u7801\u7EC4\u5408\u5F00\u9501 
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT_OR_FACE_OR_PWD=\u5361\u6216\u6307\u7EB9\u6216\u4EBA\u8138\u6216\u5BC6\u7801\u5F00\u9501
NET_ACCESS_DOOROPEN_METHOD_FACEIPCARDANDIDCARD_OR_CARD_OR_FACE=(\u8EAB\u4EFD\u8BC1+\u4EBA\u8BC1\u6BD4\u5BF9)\u6216 \u5237\u5361 \u6216 \u4EBA\u8138
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD_OR_CARD_OR_FACE=\u4EBA\u8BC1\u6BD4\u5BF9 \u6216 \u5237\u5361(\u4E8C\u7EF4\u7801) \u6216 \u4EBA\u8138

#ThemalCamera
THERMAL_CAMERA=\u70ED\u6210\u50CF
THERMAL_OPERATE=\u64CD\u4F5C
POINT_QUERY=\u67E5\u8BE2\u6D4B\u6E29\u70B9
ITEM_QUERY=\u67E5\u8BE2\u6D4B\u6E29\u9879
TEMPER_QUERY=\u67E5\u8BE2\u6E29\u5EA6
HEATMAP=\u70ED\u56FE\u4FE1\u606F
POINT_TEMPER=\u6D4B\u6E29\u70B9
ITEM_TEMPER=\u6D4B\u6E29\u9879
X=X
Y=Y
COORDINATE_ILLEGAL=\u5750\u6807\u975E\u6CD5
QUERY_RESULT=\u67E5\u8BE2\u7ED3\u679C
METER_TYPE=\u6D4B\u6E29\u9879\u7C7B\u522B
TEMPER_UNIT=\u6E29\u5EA6\u5355\u4F4D
TEMPER=\u6E29\u5EA6
UNKNOWN=\u672A\u77E5
SPOT=\u70B9
LINE=\u7EBF
AREA=\u533A\u57DF
CENTIGRADE=\u6444\u6C0F\u5EA6
FAHRENHEIT=\u534E\u6C0F\u5EA6
PRESET_ID=\u9884\u7F6E\u70B9\u7F16\u53F7
RULE_ID=\u89C4\u5219\u7F16\u53F7
TEMPER_AVER=\u5E73\u5747\u6E29\u5EA6
TEMPER_MAX=\u6700\u9AD8\u6E29\u5EA6
TEMPER_MIN=\u6700\u4F4E\u6E29\u5EA6
TEMPER_MID=\u4E2D\u95F4\u6E29\u5EA6
TEMPER_STD=\u6807\u51C6\u65B9\u5DEE
INPUT_ILLEGAL=\u8F93\u5165\u975E\u6CD5
TEMPER_INFO=\u6D4B\u6E29\u4FE1\u606F
FIVE_MINUTES=\u4E94\u5206\u949F
TEN_MINUTES=\u5341\u5206\u949F
FIFTEEN_MINUTES=\u5341\u4E94\u5206\u949F
THIRTY_MINUTES=\u4E09\u5341\u5206\u949F
SAVE_PERIOD=\u4FDD\u5B58\u5468\u671F
QUERY_LIST=\u67E5\u8BE2\u5217\u8868
RECORD_TIME=\u8BB0\u5F55\u65F6\u95F4
ITEM_NAME=\u540D\u79F0
COORDINATE=\u5750\u6807
NO_RECORD=\u65E0\u8BB0\u5F55
HEATMAP_OPERATE=\u64CD\u4F5C
IDLE=\u7A7A\u95F2
ACQUIRING=\u83B7\u53D6\u4E2D
RADIOMETRY_ATTACH=\u8BA2\u9605\u70ED\u56FE\u6570\u636E
RADIOMETRY_DETACH=\u505C\u6B62\u8BA2\u9605
RADIOMETRY_FETCH=\u83B7\u53D6\u70ED\u56FE
SAVE_HEATMAP=\u4FDD\u5B58\u70ED\u56FE
HEATMAP_METADATA_INFO=\u70ED\u56FE\u5143\u6570\u636E
HEIGHT=\u9AD8
WIDTH=\u5BBD
LENGTH=\u6570\u636E\u5927\u5C0F
SENSOR_TYPE=\u673A\u82AF\u7C7B\u578B
HEATMAP_SAVE_SUCCESS=\u70ED\u56FE\u4FDD\u5B58\u6210\u529F

# matrix screen
MATRIX_SCREEN=\u70b9\u9635\u5c4f\u4e0b\u53d1
PASSING_STATE=\u8fc7\u8f66\u72b6\u6001
PASSING_CAR=\u8fc7\u8f66
NO_CAR=\u65e0\u8f66
IN_TIME=\u5165\u573a\u65f6\u95f4
OUT_TIME=\u51fa\u573a\u65f6\u95f4
PLATE_NUMBER=\u8f66\u724c\u53f7\u7801
CAR_OWNER=\u8f66\u4e3b
PARKING_TIME=\u505c\u8f66\u65f6\u957f
USER_TYPE=\u7528\u6237\u7c7b\u578b
MONTHLY_CARD_USER=\u6708\u5361\u7528\u6237
ANNUAL_CARD_USER=\u5e74\u5361\u7528\u6237
LONG_TERM_USER=\u957f\u671f\u7528\u6237/VIP
TEMPORARY_USER=\u4e34\u65f6\u7528\u6237/Visitor
PARKING_CHARGE=\u505c\u8f66\u8d39
DAYS_DUE=\u5230\u671f\u5929\u6570
REMAINING_PARKING_SPACES=\u5269\u4f59\u8f66\u4f4d
VEHICLES_NOT_ALLOWED_TO_PASS=\u4e0d\u5141\u8bb8\u8f66\u8f86\u901a\u8fc7
ALLOWED_VEHICLES_TO_PASS=\u5141\u8bb8\u8f66\u8f86\u901a\u8fc7
SET_UP=\u8bbe\u7f6e
SUCCESSFULLY_ISSUED=\u4e0b\u53d1\u6210\u529f
DELIVERY_FAILED=\u4e0b\u53d1\u5931\u8d25
CUSTOM_USER_CLASS=\u81ea\u5b9a\u4e49\u7528\u6237\u7c7b
REMARKS_INFORMATION=\u5907\u6ce8\u4fe1\u606f
CUSTOM_INFORMATION=\u81ea\u5b9a\u4e49\u4fe1\u606f

# ����ͳ��
HUMAN_NUMBER_STATISTIC_TITLE=\u4eba\u6570\u7edf\u8ba1
HUMAN_NUMBER_STATISTIC_CONTROL=\u4eba\u6570\u7edf\u8ba1\u63a7\u5236

HUMAN_NUMBER_STATISTIC_EVENT_TITLE=\u4eba\u6570\u7edf\u8ba1\u4e8b\u4ef6\u5217\u8868

HUMAN_NUMBER_STATISTIC_EVENT_CHANNEL=\u901a\u9053
HUMAN_NUMBER_STATISTIC_EVENT_TIME=\u4e8b\u4ef6\u65f6\u523b
HUMAN_NUMBER_STATISTIC_EVENT_HOUR_IN=\u672c\u5c0f\u65f6\u8fdb
HUMAN_NUMBER_STATISTIC_EVENT_HOUR_OUT=\u672c\u5c0f\u65f6\u51fa
HUMAN_NUMBER_STATISTIC_EVENT_TODAY_IN=\u4eca\u65e5\u8fdb
HUMAN_NUMBER_STATISTIC_EVENT_TODAY_OUT=\u4eca\u65e5\u51fa
HUMAN_NUMBER_STATISTIC_EVENT_TOTAL_IN=\u603b\u5171\u8fdb
HUMAN_NUMBER_STATISTIC_EVENT_TOTAL_OUT=\u603b\u5171\u51fa

HUMAN_NUMBER_STATIC_EVENT_OSD_CLEAR=OSD\u6e05\u9664

VTO_ALARM_EVENT_ROOM_NO=\u623f\u95f4\u53f7
VTO_ALARM_EVENT_CARD_NO=\u5361\u53f7
VTO_ALARM_EVENT_TIME=\u65f6\u95f4
VTO_ALARM_EVENT_OPEN_METHOD=\u5f00\u95e8\u65b9\u5f0f
VTO_ALARM_EVENT_STATUS=\u72b6\u6001

VTO_REAL_LOAD_ROOM_NO=\u623f\u95f4\u53f7
VTO_REAL_LOAD_CARD_NO=\u5361\u53f7
VTO_REAL_LOAD_TIME=\u65f6\u95f4
VTO_REAL_LOAD_EVENT_INFO=\u4e8b\u4ef6\u4fe1\u606f

VTO_OPERATE_MANAGER_TITLE=\u5361\u4e0e\u6307\u7eb9\u4e0e\u4eba\u8138\u7ba1\u7406
VTO_OPERATE_MANAGER_REC_NO=\u7f16\u53f7
VTO_OPERATE_MANAGER_ROOM_NO=\u623f\u95f4\u53f7
VTO_OPERATE_MANAGER_CARD_NO=\u5361\u53f7
VTO_OPERATE_MANAGER_FINGER_PRINT_DATA=\u6307\u7eb9\u6570\u636e

VTO_OPERATE_INFO_TITLE=\u589e\u52a0

VTO_OPERATE_COLLECTION_FINGER_PRINT_TITLE=\u91c7\u96c6\u6307\u7eb9
DOOR_OPEN=\u5f00\u95e8
DOOR_CLOSE=\u5173\u95e8
EVENT_OPERATE=\u4e8b\u4ef6\u64cd\u4f5c
START_REAL_LOAD_PIC=\u5f00\u59cb\u667a\u80fd\u76d1\u542c
STOP_REAL_LOAD_PIC=\u505c\u6b62\u667a\u80fd\u76d1\u542c
ALARM_EVENT=\u62a5\u8b66\u4e8b\u4ef6
REAL_LOAD_EVENT=\u667A\u80FD\u4E8B\u4EF6
COLLECTION_RESULT=\u91c7\u96c6\u7ed3\u679c
NEED_FINGER_PRINT=\u9700\u8981\u6307\u7eb9
FACE_INFO=\u4eba\u8138\u4fe1\u606f
OPEN=\u6253\u5f00
VTO=\u53ef\u89c6\u5bf9\u8bb2\u95e8\u53e3\u673a
MODIFY_CARD_FACE_FAILED=\u4fee\u6539\u4eba\u8138\u5931\u8d25
EM_MASK_STATE_UNKNOWN=\u672a\u77e5
EM_MASK_STATE_NODISTI=\u672a\u8bc6\u522b
EM_MASK_STATE_NOMASK=\u6ca1\u6234\u53e3\u7f69
EM_MASK_STATE_WEAR=\u6234\u53e3\u7f69

END_SEARCH=\u67e5\u8be2\u7ed3\u675f
DOWNLOAD_PICTURE=\u4e0b\u8f7d\u56fe\u7247
ENTER_PICTURE_PATH=\u8bf7\u8f93\u5165\u56fe\u7247\u8def\u5f84
LOADING=\u8bbe\u5907\u6b63\u5728\u5904\u7406\u4e2d
REMOTE_OPEN_DOOR=\u8fdc\u7a0b\u5f00\u95e8
QUERY_CARD_EXIST_FAILED=\u68c0\u67e5\u5361\u53f7\u662f\u5426\u5b58\u5728\u5931\u8d25
CARD_EXIST=\u5361\u53f7\u5df2\u5b58\u5728
INPUT_ROOM_NO=\u8bf7\u8f93\u5165\u623f\u95f4\u53f7
ROOM_NO_EXCEED_LENGTH=\u623f\u95f4\u53f7\u8d85\u8fc7\u6700\u5927\u957f\u5ea6
REMOVE_CARD_FACE_FAILED=\u5220\u9664\u4eba\u8138\u5931\u8d25


#SCADA
SCADA=\u52a8\u73af\u4e3b\u673a
SCADA_DEVICE_LIST=\u8bbe\u5907\u8868
SCADA_POINT_LIST=\u70b9\u4f4d\u8868
SCADA_ATTACH_ALARM=\u8ba2\u9605\u62a5\u8b66
SCADA_ATTACH_INFO=\u8ba2\u9605\u4fe1\u606f
SCADA_ATTACH=\u8ba2\u9605
BTN_LIST = \u83b7\u53d6
BTN_ATTACH = \u8ba2\u9605

STOP_LISTEN_FAILED=\u505c\u6b62\u76d1\u542c\u5931\u8d25

SCREEN_NUMBER=\u5c4f\u5e55\u7f16\u53f7
TEXT_CONTENT=\u6587\u672c\u5185\u5bb9
TEXT_CONTENT_TYPE=\u6587\u672c\u7c7b\u578b
TEXT_CONTENT_COLOR=\u6587\u672c\u989c\u8272
SCROLL_TYPE=\u6eda\u52a8\u7c7b\u578b
SCROLL_SPEED=\u6eda\u52a8\u901f\u5ea6

ORDINARY=\u666e\u901a
QR_CODE=\u4e8c\u7ef4\u7801
LOCAL_TIME=\u672c\u5730\u65f6\u95f4
RESOURCE=\u8d44\u6e90\u6587\u4ef6

NOT_ROLLING=\u4e0d\u6eda\u52a8
SCROLL_LEFT_AND_RIGHT=\u5de6\u53f3\u6eda\u52a8
SCROLL_UP_AND_DOWN=\u4e0a\u4e0b\u7ffb\u9875\u6eda\u52a8

NUMBER_STRING=\u6570\u5b57\u5b57\u7b26\u4e32
VOICE_TEXT=\u8bed\u97f3\u6587\u672c
ISSUED=\u4e0b\u53d1
PROMPT=\u8bf7\u6dfb\u52a0\u5bf9\u5e94\u53c2\u6570

DEVICE_NAME=\u8bbe\u5907\u540d\u79f0
POINT_ID=\u70b9\u4f4dID
IF_VALID_SIGNAL_POINT=\u662f\u5426\u6709\u6548\u4fe1\u53f7\u70b9
POINT_NAME=\u70b9\u4f4d\u540d\u79f0
ALARM_DESCRIBE=\u62a5\u8b66\u63cf\u8ff0
ALARM_LEVEL=\u62a5\u8b66\u7ea7\u522b
ALARM_DELAY=\u62a5\u8b66\u65f6\u5ef6
ALARM_TYPE=\u62a5\u8b66\u7c7b\u578b
ALARM_TIME=\u62a5\u8b66\u65f6\u95f4
COLLECT_TIME=\u91c7\u96c6\u65f6\u95f4


