package com.lumlux.signal.daemon.parser.heinqi;

import com.lumlux.signal.daemon.bean.TblCollector;
import com.lumlux.signal.daemon.mina.BusinessConstants;
import com.lumlux.signal.daemon.mina.SignalSessionIdManager;
import com.lumlux.signal.daemon.parser.AbstractHandler;
import com.lumlux.signal.daemon.parser.hongdian.HongdianUtil;
import com.lumlux.signal.daemon.parser.inner.GetVersionHandler;
import com.lumlux.signal.daemon.rowmapper.TblCollectorRowMapper;
import com.lumlux.signal.daemon.util.ByteUtil;
import com.lumlux.signal.daemon.util.DataSourceUtil;
import com.lumlux.signal.daemon.util.ExecuteDbOperate;
import com.lumlux.signal.daemon.util.PreparedParamCallBack;
import org.apache.log4j.Logger;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;

/**
 * 下放移动式信号灯调光配置
 */
public class PutPortableDimmingHandler extends AbstractHandler {

	private static final String SQL_UPDATE_COLLECTOR = "update tbl_collector set version = ?,update_by = ?,update_date = ? where code= ? and del_flag= '0'";


	private transient Logger log = Logger.getLogger(PutPortableDimmingHandler.class);
	
	@Override
	public int handle(final byte[] data, IoSession session) {
		String threadName = Thread.currentThread().getName();
		byte[] baImei = new byte[11];
		System.arraycopy(data, 2, baImei, 0, 11);
		String strBaImei = new String(baImei);
		log.info(threadName + " [PutPortableDimmingHandler] device's baIme :" + strBaImei);
		Long sessionId = SignalSessionIdManager.getSessionId(strBaImei);
		if (sessionId != null) {
			IoSession sendSession = session.getService().getManagedSessions().get(sessionId);
			if (sendSession != null) {
				log.debug(threadName + " [PutPortableDimmingHandler] 控制器调光设置下放報警配置[session id :" + sessionId + "]");
				byte[] headContent = {(byte) 0xaa, (byte) 0xff, (byte)data[1], 0x00,0x1d, 0x01,(byte)(data[15]&0xff)
				,(byte)(data[16]&0xff),(byte)(data[17]&0xff),(byte)(data[18]&0xff),(byte)(data[19]&0xff),(byte)(data[20]&0xff)};
				byte[] mainContent = new byte[headContent.length+23]; //10+22
				byte[] endContent = {(byte) 0xaa};
				System.arraycopy(headContent, 0, mainContent, 0, headContent.length);
				System.arraycopy(endContent, 0, mainContent, mainContent.length-1, endContent.length);
//				System.out.println(mainContent);
				byte[] sendData = HongdianUtil.generateHongdianSendContent(mainContent, baImei);
//				System.out.println(mainContent);
				log.info(threadName + " [PutPortableDimmingHandler] 发送数据 : " + ByteUtil.asHex(sendData));
				sendSession.write(IoBuffer.wrap(sendData));

			}
		/*	DataSourceUtil.execute(new ExecuteDbOperate()
			{

				@Override
				public int execute(Connection connection) throws SQLException
				{
					Timestamp timestamp = new Timestamp(System.currentTimeMillis());

							log.debug(threadName + " [PutPortableDimmingHandler] Execute SQL ===> " + SQL_UPDATE_COLLECTOR);
							PreparedStatement pst = connection.prepareStatement(SQL_UPDATE_COLLECTOR);
							pst.setString(1, "1");
							pst.setString(2, "1");
							pst.setTimestamp(3, timestamp);
							pst.setString(4, strBaImei);
							pst.execute();
							pst.close();

					return 0;
				}
			});*/

		}
		return 0;
	}
}
