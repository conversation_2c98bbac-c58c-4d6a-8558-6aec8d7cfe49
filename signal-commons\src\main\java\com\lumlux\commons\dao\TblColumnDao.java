package com.lumlux.commons.dao;

import com.heinqi.yangtes.jee.base.AbstractCondition;
import com.heinqi.yangtes.jee.commons.persistence.CrudDao;
import com.heinqi.yangtes.jee.commons.persistence.annotation.MyBatisDao;
import com.lumlux.commons.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
@MyBatisDao
public interface TblColumnDao extends CrudDao<TblColumn> {
    public List<TblColumn> selectVariablesByArea(AbstractCondition<TblColumn> condition);

    @Select("select * from tbl_column WHERE id= #{id} and del_flag = '0'")
    Map<String, Object> byIdColumn(@Param("id")String id);

    @Update("UPDATE tbl_column SET `update_date` = #{updateDate},`update_by` = #{updateBy},attitude_status = '0',initial_x_shaft =#{initialXshaft},initial_y_shaft =#{initialYshaft},initial_z_shaft = #{initialZshaft} WHERE  id = #{id} and del_flag = '0'")
    boolean resetShaft(@Param("updateDate")String updateDate,@Param("updateBy")String updateBy,@Param("id")String id,@Param("initialXshaft")double initialXshaft,@Param("initialYshaft")double initialYshaft,@Param("initialZshaft")double initialZshaft);

    @Update("\t\t\tUPDATE `tbl_failure` SET `end_date`= #{endDate},`status`= '2',`update_by` = #{updateBy},`update_date` = #{updateDate} WHERE `collector_id` = #{id} and del_flag = '0' and description = '位姿偏移' and `status` != '2'")
    boolean resetFailure(@Param("endDate")String endDate,@Param("updateBy")String updateBy,@Param("updateDate")String updateDate,@Param("id")String id);
    @Update("update tbl_column set thresholdvalue_x_shaft=#{xShaft}, thresholdvalue_y_shaft=#{yShaft}, thresholdvalue_z_shaft=#{zShaft}, update_by=#{updateBy}, update_date=#{updateDate}\n" +
            "\t\tWHERE\n" +
            "\t\t\tid = #{id}")
    boolean updateAttitude(@Param("xShaft")String xShaft, @Param("yShaft")String yShaft, @Param("zShaft")String zShaft,
                               @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,@Param("id")String id);

    boolean updateColumnConfig(@Param("stateInterval")String stateInterval, @Param("statusOfflineInterval")String statusOfflineInterval, @Param("statusPartInvalidInterval")String statusPartInvalidInterval,
                               @Param("updateBy")String updateBy, @Param("updateDate") String updateDate, @Param("id")String id);

    boolean updateDisplay(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                       @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                       @Param("remarks")String remarks,@Param("id")String id);
    boolean updateHorn(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                       @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                       @Param("remarks")String remarks,@Param("id")String id);
    boolean updateProjector(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                            @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                            @Param("remarks")String remarks,@Param("id")String id,@Param("type")String type);

    boolean updateColumn(@Param("model")String model,@Param("name")String name, @Param("code")String code, @Param("areaId")String areaId,
                         @Param("enabled")String enabled,@Param("supplierId")String supplierId, @Param("supportId")String supportId, @Param("officeId")String officeId,
                         @Param("prodDeliveryTime") String prodDeliveryTime, @Param("prodEndTime")String prodEndTime,
                         @Param("deviceInstallTime")String deviceInstallTime, @Param("iccid")String iccid, @Param("attitudeEnabled")String attitudeEnabled,
                         @Param("longitude")String longitude, @Param("latitude")String latitude,@Param("qnlineThreshold")String qnlineThreshold,
                         @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                         @Param("address")String address, @Param("remarks")String remarks,@Param("redProjectorEnabled")String redProjectorEnabled,
                         @Param("greenProjectorEnabled")String greenProjectorEnabled,
                         @Param("hornEnabled")String hornEnabled,@Param("displayEnabled")String displayEnabled,@Param("id")String id);


    String  listCode(@Param("code") String code);

    public List<TblFailure> getFailureHistorys(AbstractCondition<TblFailure> condition);

    public List<TblColumnHistory> listColumnHistory(AbstractCondition<TblColumnHistory> condition);
    @Select(" SELECT * from tbl_projector WHERE column_id=#{columnId}")
    List<Map<String, Object>>  listProjector(@Param("columnId") String columnId);
    @Select(" SELECT * from tbl_horn WHERE column_id=#{columnId}")
    List<Map<String, Object>>  listHorn(@Param("columnId") String columnId);
    @Select(" SELECT * from tbl_projector WHERE column_id=#{columnId}")
    List<Map<String, Object>>  listDisplay(@Param("columnId") String columnId);
    @Select(" SELECT state_interval,status_offline_interval,status_part_invalid_interval  from tbl_alarm_config WHERE collector_id=#{columnId}")
    List<Map<String, Object>>  listColumnConfig(@Param("columnId") String columnId);

    @Update(" UPDATE  tbl_column SET del_flag ='1'  WHERE id=#{columnId} and del_flag ='0'")
    boolean  deleteColumn(@Param("columnId") String columnId);

    @Update(" UPDATE tbl_projector SET del_flag ='1'  WHERE column_id=#{columnId} and del_flag ='0'")
    boolean  deleteProjector(@Param("columnId") String columnId);

    @Update("UPDATE tbl_horn SET del_flag ='1'  WHERE column_id=#{columnId} and del_flag ='0'")
    boolean  deleteHorn(@Param("columnId") String columnId);

    @Update("UPDATE tbl_display SET del_flag ='1'  WHERE column_id=#{columnId} and del_flag ='0'")
    boolean  deleteDisplay(@Param("columnId") String columnId);
    boolean addDisplay(@Param("id")String id, @Param("columnId")String columnId,
                       @Param("planType")String planType,@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                       @Param("createBy")String createBy, @Param("createDate")String createDate, @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                       @Param("remarks")String remarks,@Param("delFlag")String delFlag);
    boolean addHorn(@Param("id")String id, @Param("columnId")String columnId,
                    @Param("planType")String planType,@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                    @Param("createBy")String createBy, @Param("createDate")String createDate, @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                    @Param("remarks")String remarks,@Param("delFlag")String delFlag);
    boolean addProjector(@Param("id")String id, @Param("columnId")String columnId,@Param("planType")String planType ,@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("plan")String plan,
                         @Param("createBy")String createBy, @Param("createDate")String createDate, @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                         @Param("remarks")String remarks,@Param("delFlag")String delFlag,@Param("projectorId")String projectorId,@Param("type")String type);
    boolean addColumn(@Param("id")String id, @Param("name")String name,@Param("code")String code, @Param("model")String model,  @Param("areaId")String areaId,
                      @Param("enabled")String enabled,@Param("status")String status,@Param("supplierId")String supplierId, @Param("supportId")String supportId, @Param("officeId")String officeId,
                      @Param("failureStatus")String failureStatus,@Param("failureDate")String failureDate, @Param("prodDeliveryTime") String prodDeliveryTime, @Param("prodEndTime")String prodEndTime,
                      @Param("prodWarrantyStatus")String prodWarrantyStatus,@Param("deviceInstallTime")String deviceInstallTime, @Param("iccid")String iccid, @Param("attitudeEnabled")String attitudeEnabled,
                      @Param("xShaft")String xShaft,@Param("yShaft")String yShaft,@Param("zShaft")String zShaft,@Param("columnStatus")String columnStatus,@Param("columnPower")String columnPower,
                      @Param("longitude")String longitude, @Param("latitude")String latitude,@Param("qnlineThreshold")String qnlineThreshold,
                      @Param("createBy")String createBy, @Param("createDate")String createDate, @Param("updateBy")String updateBy, @Param("updateDate")String updateDate,
                      @Param("address")String address, @Param("remarks")String remarks,@Param("delFlag")String delFlag,
                      @Param("redProjectorId")String redProjectorId, @Param("redProjectorEnabled")String redProjectorEnabled,@Param("redProjectorStatus")String redProjectorStatus,
                      @Param("redProjectorPower")String redProjectorPower, @Param("greenProjectorId")String greenProjectorId,@Param("greenProjectorEnabled")String greenProjectorEnabled,
                      @Param("greenProjectorStatus")String greenProjectorStatus, @Param("greenProjectorPower")String greenProjectorPower,@Param("hornEnabled")String hornEnabled,
                      @Param("hornStatus")String hornStatus, @Param("displayEnabled")String displayEnabled,@Param("displayStatus")String displayStatus,@Param("attitudeStatus")String attitudeStatus
                      );


    void enableColumn(String enabled, String id,String model);

    void enableRedProjector(String redProjectorEnabled, String id,String model);

    void enableGreenProjector(String greenProjectorEnabled, String id,String model);

    void enableHorn(String hornEnabled,String id,String model);

    void enableDisplay(String displayEnabled,String id,String model);

    void enableAttitude(String attitudeEnabled,String id,String model);
    @Select(" SELECT * from tbl_projector WHERE column_id=#{columnId} and del_flag='0' and type=#{type} ")
    List<TblProjector> listProjectorRed(@Param("columnId")String columnId, @Param("type")String type);

    @Select(" SELECT * from tbl_projector WHERE column_id=#{columnId} and del_flag='0' and type=#{type} ")
    List<TblProjector> listProjectorGreen(@Param("columnId")String columnId, @Param("type")String type);


    @Select(" SELECT * from tbl_horn WHERE column_id=#{columnId} and del_flag='0' ")
    List<TblHorn> listHorns(@Param("columnId")String columnId);

    @Select(" SELECT * from tbl_display WHERE column_id=#{columnId} and del_flag='0' ")
    List<TblDisplay> listDisplays(@Param("columnId")String columnId);

    TblColumn getThresholdXYZ(@Param("columnId")String id);

    List<Map<String, Object>> listFailureHistorys(@Param("supportId") String supportId,@Param("description") String description,@Param("columnName") String columnName,@Param("startDate") String startDate,@Param("endDate") String endDate);

    List<ColumnSupplier> getSupplyAnaly();

    int columnSupplyStatus(int model);

    int getColumnSupplyStatusInWar(Integer model);

    Integer getColumnSupplyStatusOutWar(Integer model);

    Integer getColumnSupplyStatusFailure(Integer model);

    Integer getColumnSupplyStatusOnline(Integer model);

    Integer getControllerSum();

    Integer getColumnSupplyStatusShaft(Integer model);

    List<String> findCode();

    Integer getCrossroadSum();

    Integer getProSum();

    List<Map<String, Object>> getColInfo();

}