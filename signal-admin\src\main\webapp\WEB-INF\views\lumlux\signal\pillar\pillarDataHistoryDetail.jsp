<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>
<%@ taglib prefix="ui" tagdir="/WEB-INF/tags/ui" %>

<layout:default title="立柱历史管理">
    <style>
        .longContent {
            width: 18%;
            max-width: 235px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
        .controls >a {
            -webkit-user-drag: none;
        }
        .controls >a >input{
            border:1px solid !important;
            margin: 0 2px 5px 5px !important;
        }
        .controls >a >input:last-child {
            border:1px solid !important;
            margin: 0 5px 5px 2px !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function() {
            $("#searchForm").validate({
                submitHandler: function(form){
                    loading('正在查询，请稍等...');
                    form.submit();
                },
                errorContainer: "#messageBox",
                errorPlacement: function(error, element) {
                    $("#messageBox").text("输入有误，请先更正。");
                    if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
                        error.appendTo(element.parent().parent());
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });
        function page(n,s){
            $("#pageNo").val(n);
            $("#pageSize").val(s);
            $("#searchForm").submit();
            return false;
        }



    </script>

    <ul class="nav nav-tabs">
        <li><a href="${ctx}/signal/tblCollector/listColumn?condition.model=13 & condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">立柱列表</a></li>
        <li class="active"><a href="${ctx}/signal/tblAlarmConfig/listColumnHistory?condition.collectorId=${form.condition.collectorId}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}&condition.startDate=&condition.endDate=">历史详情</a></li>
    </ul>
    <div id="topDesc">
        详细配置 > <span>${form.condition.collectorName}</span>
    </div>

   <form:form id="searchForm" modelAttribute="form" action="${ctx}/signal/tblAlarmConfig/listColumnHistory?condition.model=13&condition.startDate=&condition.endDate="
              method="post" class="breadcrumb form-search">
       <input id="pageNo" name="condition.page.pageNo" type="hidden" value="${form.condition.page.pageNo}"/>
       <input id="pageSize" name="condition.page.pageSize" type="hidden" value="${form.condition.page.pageSize}"/>
       <input id="dataEntityId" name="dataEntity.id" type="hidden" value=""/>
       <input id="model" name="dataEntity.model" type="hidden" value="13"/>
       <input id="collectorId" name="condition.collectorId" type="hidden" value="${form.condition.collectorId}"/>
        <input id="areaId" name="condition.areaId" type="hidden" value="${form.condition.areaId}"/>
       <input id="startDate" name="condition.startDate" type="hidden" value="${form.condition.startDate}"/>
    </form:form>

    <sys:message content="${message}"/>
    <table id="contentTable" class="table table-striped table-bordered table-condensed">
        <thead>
        <tr>
            <th>名称</th>
            <th>红投影功率(w)</th>
            <th>红投影状态</th>
            <th>绿投影功率(w)</th>
            <th>绿投影状态</th>
            <th>总功率(w)</th>
            <th>立柱供电状态</th>
            <th>音响状态</th>
            <th>显示屏状态</th>
            <th>经度</th>
            <th>纬度</th>
            <th>当前x轴</th>
            <th>当前y轴</th>
            <th>当前z轴</th>
            <th>创建时间</th>
          <%--  <shiro:hasAnyPermissions name="signal:tblCollector:edit,signal:tblCollector:delete">
                <th>操作</th>
            </shiro:hasAnyPermissions>--%>
        </tr>
        </thead>
        <tbody>

        <c:forEach items="${form.condition.page.list}" var="tblColumn">
            <tr>
                <td>${tblColumn.name}</td>
                <td>${tblColumn.redProjectorPower}</td>
                <td>${tblColumn.redProjectorStatus == 0  ? "关":(tblColumn.redProjectorStatus == 2 ? "红投影仪故障":(tblColumn.redProjectorStatus == 1 ? "开":"红投影仪时段方案没有执行"))}</td>
                <td>${tblColumn.greenProjectorPower}</td>
                <td>${tblColumn.greenProjectorStatus == 0  ? "关":(tblColumn.greenProjectorStatus == 2 ? "绿投影仪故障":(tblColumn.greenProjectorStatus == 1 ? "开":"绿投影仪时段方案没有执行"))}</td>
                <td>${tblColumn.columnPower}</td>
                <td>${tblColumn.columnStatus == 0  ? "正常":(tblColumn.columnStatus == 1 ? "信控异常":(tblColumn.columnStatus == 2 ? "信控异常":"学习中"))}</td>
<%--                <td>${tblColumn.columnStatus== 0 ?"正常":"断电"}</td>--%>
                <td>${tblColumn.hornStatus == 2  ? "喇叭时段方案没有执行":(tblColumn.hornStatus == 0 ? "关":"开")}</td>
                <td>${tblColumn.displayStatus == 0 ?"正常":"故障"}</td>
                <td>${tblColumn.longitude}</td>
                <td>${tblColumn.latitude}</td>
                <td>${tblColumn.attitudeXshaft}</td>
                <td>${tblColumn.attitudeYshaft}</td>
                <td>${tblColumn.attitudeZshaft}</td>
                <td><fmt:formatDate value="${tblColumn.createDate}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
    <div class="pagination">${form.condition.page}</div>
</layout:default>
