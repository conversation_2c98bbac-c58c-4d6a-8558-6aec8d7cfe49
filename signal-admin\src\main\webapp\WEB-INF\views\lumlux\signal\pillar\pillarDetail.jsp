<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>


<layout:default title="参数配置管理">
    <%--    <link href="${ctxStatic}/bootstrap/css/bootstrap-switch-3.3.4.css" rel="stylesheet">--%>
    <link href="${ctxStatic}/font-awesome/css/font-awesome.min.css" type="text/css" rel="stylesheet" />
    <script src="${ctxStatic}/bootstrap/js/bootstrap-switch-3.3.4.js"></script>
    <link href="${ctxStatic}/bootstrap/css/bootstrap-3.3.7.css" rel="stylesheet">
    <style>

        body,html{
            overflow: hidden;
            height: 100%;
        }

        #container{
            width: 100%;
            height: 100%;
            gap: 10px;
        }

        #container div{
            width: 50%;
            height: 50%;
            float: left;
            border: 1px solid #ccc;
            margin: auto;

        }
        #topDesc {
            height: 35px;
        }

         #btnCancelTop {
             float: right;
           /*  top: 2px;
             right: 110px;
             bottom: 0;
             left: 100px;*/
         }
     /*   #container div:hover{
            opacity: .8;
            filter: alpha(opacity=80);
        }*/
        .input-width-20{
            width:30px;
            text-align: center;
        }

    </style>

    <script>

        $().ready(function(){
            $(".input-width-20").blur(function(){
                if(this.value.length==0){
                    this.value="00";
                }else if(this.value.length==1){
                    this.value= "0" + this.value;
                }else if(this.value.length==2){
                    if($(this).hasClass("type-hour")){
                        if(parseInt(this.value)<0){
                            this.value="00";
                        }else if(parseInt(this.value)>23){
                            this.value="23";
                        }
                    }else if($(this).hasClass("type-minute")){
                        if(parseInt(this.value)<0){
                            this.value="00";
                        }else if(parseInt(this.value)>59){
                            this.value="59";
                        }
                    } else if($(this).hasClass("type-second")){
                        if(parseInt(this.value)<0){
                            this.value="00";
                        }else if(parseInt(this.value)>59){
                            this.value="59";
                        }
                    }
                }
            });


        });


        var set = function(){
            top.$.jBox.tip("正在下放，请稍等...",'loading',{
                opacity:0,
                top: '100px'
            });
            let code = "${form.condition.code}";
            const collector ={
                "code":code
            }
            params = {...collector}
            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/secondPutRed",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(params),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("下放成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("下放失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0, top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("下放失败", 'error', {opacity: 0, top: '100px'})
            })
        }

        var onSaveBtnClick = function() {
            top.$.jBox.tip("正在保存，请稍等...",'loading',{
                opacity:0,
                top: '100px'
            });
            let  tblProjectorPlanIdsList =  $("input[id*=tblProjectorPlanIds]");
            console.log(tblProjectorPlanIdsList)
            let id1 = tblProjectorPlanIdsList[0].value;
            let id2 = tblProjectorPlanIdsList[1].value;
            let id3 = tblProjectorPlanIdsList[2].value;
            let  tblProjectorStartTimeList =  $("input[id*=tblProjectorStartTime]");
            // console.log(tblProjectorStartTimeList)
            let startTime1 = tblProjectorStartTimeList[0].value +":"+tblProjectorStartTimeList[1].value +":"+ tblProjectorStartTimeList[2].value;
            let startTime2 = tblProjectorStartTimeList[3].value +":"+tblProjectorStartTimeList[4].value +":"+ tblProjectorStartTimeList[5].value;
            let startTime3 = tblProjectorStartTimeList[6].value +":"+tblProjectorStartTimeList[7].value +":"+ tblProjectorStartTimeList[8].value;
            // console.log(startTime1+  "," + startTime2 +  "," + startTime3);
            let  tblProjectorEndTimeList =  $("input[id*=tblProjectorEndTime]");
            let endTime1 = tblProjectorEndTimeList[0].value +":"+tblProjectorEndTimeList[1].value +":"+ tblProjectorEndTimeList[2].value;
            let endTime2 = tblProjectorEndTimeList[3].value +":"+tblProjectorEndTimeList[4].value +":"+ tblProjectorEndTimeList[5].value;
            let endTime3 = tblProjectorEndTimeList[6].value +":"+tblProjectorEndTimeList[7].value +":"+ tblProjectorEndTimeList[8].value;
            /*  let  tblProjectorPlanTypesList =  $("input[id*=tblProjectorPlanTypes]");
            tblProjectorPlanSelect_
              let planType1 = tblProjectorPlanTypesList[0].value;
              let planType2 = tblProjectorPlanTypesList[1].value;
              let planType3 = tblProjectorPlanTypesList[2].value;*/
            // console.log(planType1+  "," + planType2 +  "," + planType3);
            let  plan1=   $($("select[id*=tblProjectorPlanSelect]")[0]).children()[0].selected == false ? 0:1;
            let plan2=   $($("select[id*=tblProjectorPlanSelect]")[1]).children()[0].selected == false ? 0:1
            let plan3=   $($("select[id*=tblProjectorPlanSelect]")[2]).children()[0].selected == false ? 0:1
            console.log(plan1+  "," + plan2 +  "," + plan3)

            var list =[];
            var a ={};
            a.startTime = startTime1;
            a.endTime = endTime1;
            a.plan = plan1;
            a.type='0';
            a.id = id1;
            list.push(a);
            var b ={};
            b.startTime = startTime2;
            b.endTime = endTime2;
            b.plan = plan2;
            b.type='0';
            b.id = id2;
            list.push(b);
            var c ={};
            c.startTime = startTime3;
            c.endTime = endTime3;
            c.plan = plan3;
            c.type='0';
            c.id = id3;
            list.push(c);
            console.log(list);

            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/updateProjector",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(list),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("保存成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0, top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0, top: '100px'})
            })
        }
        // 绿投影下放
        var setGreenPro = function(){
            top.$.jBox.tip("正在下放，请稍等...",'loading',{
                opacity:0,
                top: '100px'
            });
            let code = "${form.condition.code}";
            const collector ={
                "code":code
            }
            params = {...collector}
            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/secondPutGreen",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(params),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("下放成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("下放失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0, top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("下放失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }

        var onSaveGreenProBtnClick = function() {
            let  tblProjectorGreenPlanIdsList =  $("input[id*=tblProjectorGreenPlanIds]");

            let greenId1 = tblProjectorGreenPlanIdsList[0].value;
            let greenId2 = tblProjectorGreenPlanIdsList[1].value;
            let greenId3 = tblProjectorGreenPlanIdsList[2].value;
            let  tblProjectorGreenStartTimeList =  $("input[id*=tblProjectorGreenStartTime]");
            let greenStartTime1 = tblProjectorGreenStartTimeList[0].value +":"+tblProjectorGreenStartTimeList[1].value +":"+ tblProjectorGreenStartTimeList[2].value;
            let greenStartTime2 = tblProjectorGreenStartTimeList[3].value +":"+tblProjectorGreenStartTimeList[4].value +":"+ tblProjectorGreenStartTimeList[5].value;
            let greenStartTime3 = tblProjectorGreenStartTimeList[6].value +":"+tblProjectorGreenStartTimeList[7].value +":"+ tblProjectorGreenStartTimeList[8].value;
            let  tblProjectorGreenEndTimeList =  $("input[id*=tblProjectorGreenEndTime]");
            let greenEndTime1 = tblProjectorGreenEndTimeList[0].value +":"+tblProjectorGreenEndTimeList[1].value +":"+ tblProjectorGreenEndTimeList[2].value;
            let greenEndTime2 = tblProjectorGreenEndTimeList[3].value +":"+tblProjectorGreenEndTimeList[4].value +":"+ tblProjectorGreenEndTimeList[5].value;
            let greenEndTime3 = tblProjectorGreenEndTimeList[6].value +":"+tblProjectorGreenEndTimeList[7].value +":"+ tblProjectorGreenEndTimeList[8].value;
            let greenPlan1=  $($("select[id*=tblProjectorGreenPlanSelect]")[0]).children()[0].selected == false ? 0:1;
            let greenPlan2=   $($("select[id*=tblProjectorGreenPlanSelect]")[1]).children()[0].selected == false ? 0:1
            let greenPlan3=   $($("select[id*=tblProjectorGreenPlanSelect]")[2]).children()[0].selected == false ? 0:1

            var list =[];
            var a ={};
            a.startTime = greenStartTime1;
            a.endTime = greenEndTime1;
            a.plan = greenPlan1;
            a.type='1';
            a.id = greenId1;
            list.push(a);
            var b ={};
            b.startTime = greenStartTime2;
            b.endTime = greenEndTime2;
            b.plan = greenPlan2;
            b.type='1';
            b.id = greenId2;
            list.push(b);
            var c ={};
            c.startTime = greenStartTime3;
            c.endTime = greenEndTime3;
            c.plan = greenPlan3;
            c.type='1';
            c.id = greenId3;
            list.push(c);

            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/updateProjector",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(list),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("保存成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0 ,top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }
        // 音响下放
        var setHorn = function(){
            top.$.jBox.tip("正在下放，请稍等...",'loading',{
                opacity:0,
                top: '100px'
            });
            let code = "${form.condition.code}";
            const collector ={
                "code":code
            }
            params = {...collector}
            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/secondPutHorn",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(params),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("下放成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("下放失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0 ,top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("下放失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }

        var onSaveHornBtnClick = function() {
            let  tblHornsIdsList =  $("input[id*=tblHornsIds]");

            let hornId1 = tblHornsIdsList[0].value;
            let hornId2 = tblHornsIdsList[1].value;
            let hornId3 = tblHornsIdsList[2].value;
            let  tblHornsStartTimeList =  $("input[id*=tblHornsStartTime]");
            let hornStartTime1 = tblHornsStartTimeList[0].value +":"+tblHornsStartTimeList[1].value +":"+ tblHornsStartTimeList[2].value;
            let hornStartTime2 = tblHornsStartTimeList[3].value +":"+tblHornsStartTimeList[4].value +":"+ tblHornsStartTimeList[5].value;
            let hornStartTime3 = tblHornsStartTimeList[6].value +":"+tblHornsStartTimeList[7].value +":"+ tblHornsStartTimeList[8].value;
            let  tblHornsEndTimeList =  $("input[id*=tblHornsEndTime]");
            let hornEndTime1 = tblHornsEndTimeList[0].value +":"+tblHornsEndTimeList[1].value +":"+ tblHornsEndTimeList[2].value;
            let hornEndTime2 = tblHornsEndTimeList[3].value +":"+tblHornsEndTimeList[4].value +":"+ tblHornsEndTimeList[5].value;
            let hornEndTime3 = tblHornsEndTimeList[6].value +":"+tblHornsEndTimeList[7].value +":"+ tblHornsEndTimeList[8].value;
            let hornPlan1=  $($("select[id*=tblHornsPlanSelect]")[0]).children()[0].selected == false ? 0:1;
            let hornPlan2=   $($("select[id*=tblHornsPlanSelect]")[1]).children()[0].selected == false ? 0:1
            let hornPlan3=   $($("select[id*=tblHornsPlanSelect]")[2]).children()[0].selected == false ? 0:1

            var list =[];
            var a ={};
            a.startTime = hornStartTime1;
            a.endTime = hornEndTime1;
            a.plan = hornPlan1;
            a.id = hornId1;
            list.push(a);
            var b ={};
            b.startTime = hornStartTime2;
            b.endTime = hornEndTime2;
            b.plan = hornPlan2;
            b.id = hornId2;
            list.push(b);
            var c ={};
            c.startTime = hornStartTime3;
            c.endTime = hornEndTime3;
            c.plan = hornPlan3;
            c.id = hornId3;
            list.push(c);

            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/updateHorn",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(list),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("保存成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
                    }
                },
                error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }


        // 显示屏下放
        var setDisplay = function(){
            top.$.jBox.tip("正在下放，请稍等...",'loading',{
                opacity:0,
                top: '100px'
            });
            let code = "${form.condition.code}";
            const collector ={
                "code":code
            }
            params = {...collector}
            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/secondPutDisplay",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(params),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("下放成功", 'success', {
                            opacity: 0,
                            top: '100px'
                        });
                    } else {
                        top.$.jBox.tip("下放失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0 ,top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("下放失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }

        var onSaveDisplayBtnClick = function() {
            let  tblDisplayIdsList =  $("input[id*=tblDisplayIds]");

            let displayId1 = tblDisplayIdsList[0].value;
            let displayId2 = tblDisplayIdsList[1].value;
            let displayId3 = tblDisplayIdsList[2].value;
            let  tblDisplaysStartTimeList =  $("input[id*=tblDisplaysStartTime]");
            let diplaysStartTime1 = tblDisplaysStartTimeList[0].value +":"+tblDisplaysStartTimeList[1].value +":"+ tblDisplaysStartTimeList[2].value;
            let diplaysStartTime2 = tblDisplaysStartTimeList[3].value +":"+tblDisplaysStartTimeList[4].value +":"+ tblDisplaysStartTimeList[5].value;
            let diplaysStartTime3 = tblDisplaysStartTimeList[6].value +":"+tblDisplaysStartTimeList[7].value +":"+ tblDisplaysStartTimeList[8].value;
            let  tblDisplaysEndTimeList =  $("input[id*=tblDisplaysEndTime]");
            let diplaysEndTime1 = tblDisplaysEndTimeList[0].value +":"+tblDisplaysEndTimeList[1].value +":"+ tblDisplaysEndTimeList[2].value;
            let diplaysEndTime2 = tblDisplaysEndTimeList[3].value +":"+tblDisplaysEndTimeList[4].value +":"+ tblDisplaysEndTimeList[5].value;
            let diplaysEndTime3 = tblDisplaysEndTimeList[6].value +":"+tblDisplaysEndTimeList[7].value +":"+ tblDisplaysEndTimeList[8].value;
            let diplaysPlan1=  $($("select[id*=tblDisplaysPlanSelect]")[0]).children()[0].selected == false ? 0:1;
            let diplaysPlan2=   $($("select[id*=tblDisplaysPlanSelect]")[1]).children()[0].selected == false ? 0:1
            let diplaysPlan3=   $($("select[id*=tblDisplaysPlanSelect]")[2]).children()[0].selected == false ? 0:1

            var list =[];
            var a ={};
            a.startTime = diplaysStartTime1;
            a.endTime = diplaysEndTime1;
            a.plan = diplaysPlan1;
            a.id = displayId1;
            list.push(a);
            var b ={};
            b.startTime = diplaysStartTime2;
            b.endTime = diplaysEndTime2;
            b.plan = diplaysPlan2;
            b.id = displayId2;
            list.push(b);
            var c ={};
            c.startTime = diplaysStartTime3;
            c.endTime = diplaysEndTime3;
            c.plan = diplaysPlan3;
            c.id = displayId3;
            list.push(c);

            $.ajax({
                type: "post",
                url: "${path}/column/a/signal/tblColumn/updateDisplay",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify(list),
                success: result => {
                    if(result.code == 200) {
                        top.$.jBox.tip("保存成功", 'success', {
                            opacity: 0,
                            // bottom: '-10px',
                            top: '100px'
                            });
                    } else {
                        top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0 ,top: '100px'});
                    }
                },
                error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0 ,top: '100px'})
            })
        }

        function editColumn(){
            // var a =window.location.href.split("=")[1].replace("condition.areaId", "");
            var a =window.location.href;
            console.log(a);
            $("#model").val();
            $("#dataEntityId").val(window.location.href.split("=")[1].replace("&condition.model", ""));
            $("#searchForm").attr("action","${ctx}/signal/tblCollector/columnForm");
            <%--$("#searchForm").attr("action","${ctx}/signal/tblCollector/form");--%>
            $("#searchForm").submit();
            return false;
        }

    </script>

    <ul class="nav nav-tabs">
        <li><a href="${ctx}/signal/tblCollector/listColumn?condition.model=13 &condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">立柱列表</a></li>
<%--        <li><a href="${ctx}/signal/tblAlarmConfig/secondDetailLie?condition.collectorId=${form.dataEntity.collectorId}">参数配置</a></li>--%>
<%--        <li><a id="" class="" onclick="javascript:window.history.back();return false;">修改立柱</a></li>--%>
        <li><a href="#" onclick="editColumn()">修改立柱</a></li>
        <li class="active">
            <a><shiro:hasPermission name="signal:tblColumn:edit">详细配置</shiro:hasPermission></a>
        </li>
    </ul>
    <form id="searchForm" style="display: none">
        <input id="dataEntityId" name="dataEntity.id" value=""/>
        <input id="model" name="dataEntity.model" value="13"/>
<%--        <form:hidden path="dataEntity.model" value="13"/>--%>
    </form>
    <div id="topDesc">
       详细配置 > <span>${form.condition.collectorName}</span>
        <a id="btnCancelTop" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>
    </div>



    <div id="container">
        <div  id="redProjector">
            <h1 style="text-align: center">红投影配置</h1>
            <table id="contentRedTable" class="table table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th>时段类型</th>
                    <th>控制灯色</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>控制方案</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${tblProjectorReds}" var="tblProjectorReds">
                <tr>
                    <td class="hide"><input id="tblProjectorPlanIds_${tblProjectorReds.id}"  value="${tblProjectorReds.id}" type="hidden"/></td>
                    <td>${tblProjectorReds.planType == 0 ? "时段1": tblProjectorReds.planType == 1 ? "时段2":"时段3"}</td>
                        <%--  <td><input id="tblProjectorPlanTypes_${tblProjectorReds.planType}"  value="${tblProjectorReds.planType == 0 ? "时段1": tblProjectorReds.planType == 1 ? "时段2":"时段3"}" readonly/></td>--%>
                        <%--  <td><input type="text" value="${tblProjectorReds.type==0 ? "红色" :"绿色"}"/></td>--%>
                    <td>${tblProjectorReds.type==0 ? "红色" :"绿色"}</td>
                    <td>
                     <input id="tblProjectorStartTime1_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblProjectorReds.startTime.split(':')[0]}"/> <b>:</b>
                    <input id="tblProjectorStartTime2_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblProjectorReds.startTime.split(':')[1]}"/> <b>:</b>
                    <input id="tblProjectorStartTime3_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-second number" value="${tblProjectorReds.startTime.split(':')[2]}"/> </b>
                    </td>
                    <td>
                        <input id="tblProjectorEndTime1_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-hour number"   value="${tblProjectorReds.endTime.split(':')[0]}"/> <b>:</b>
                        <input id="tblProjectorEndTime2_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblProjectorReds.endTime.split(':')[1]}"/> <b>:</b>
                        <input id="tblProjectorEndTime3_${tblProjectorReds.id}" maxlength="2" class="input-width-20 type-second number" value="${tblProjectorReds.endTime.split(':')[2]}"/> </b>
                    </td>
                    <td>
                        <select id="tblProjectorPlanSelect_${tblProjectorReds.id}" class="selectpicker" style="width: 100px" >
                            <c:choose>
                                <c:when test="${tblProjectorReds.plan == 0}"><!-- 如果 -->
                                    <option value="1"> 关 </option>
                                    <option selected="selected" value="0"> 开 </option>
                                </c:when>
                                <c:otherwise>
                                    <option selected="selected" value="1"> 关 </option>
                                    <option value="0"> 开 </option>
                                </c:otherwise>
                            </c:choose>
                        </select>
                    </td>
                </tr>
                </c:forEach>
                </tbody>
            </table>
<%--            <div style="height: auto;border: 0px">--%>
            <div style="height: auto;border: 0px;text-align: center" class="typeRed" >
                <shiro:hasPermission name="signal:tblColumn:edit">
                    <input id="btnSubmit"   class="btn btn-primary" type="submit" value="保 存" onclick="onSaveBtnClick()"/>&nbsp;
                </shiro:hasPermission>
                <shiro:hasPermission name="signal:tblColumn:put">
                    <a id="setBtn" onclick="set()"  class="btn btn-primary">下 放</a>
                </shiro:hasPermission>
<%--                <a id="btnCancel" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>--%>
           </div>

        </div>

        <div  id="greenProjector">
            <h1 style="text-align: center">绿投影配置</h1>
            <table id="contentGreenTable" class="table table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th>时段类型</th>
                    <th>控制灯色</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>控制方案</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${tblProjectorGreens}" var="tblProjectorGreens">
                    <tr>
                        <td class="hide"><input id="tblProjectorGreenPlanIds_${tblProjectorGreens.id}"  value="${tblProjectorGreens.id}" type="hidden"/></td>
                        <td>${tblProjectorGreens.planType == 0 ? "时段1": tblProjectorGreens.planType == 1 ? "时段2":"时段3"}</td>
                        <td>${tblProjectorGreens.type==0 ? "红色" :"绿色"}</td>
                        <td>
                            <input id="tblProjectorGreenStartTime1_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblProjectorGreens.startTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblProjectorGreenStartTime2_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblProjectorGreens.startTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblProjectorGreenStartTime3_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-second number" value="${tblProjectorGreens.startTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <input id="tblProjectorGreenEndTime1_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblProjectorGreens.endTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblProjectorGreenEndTime2_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblProjectorGreens.endTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblProjectorGreenEndTime3_${tblProjectorGreens.id}" maxlength="2" class="input-width-20 type-second number" value="${tblProjectorGreens.endTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <select id="tblProjectorGreenPlanSelect_${tblProjectorGreens.id}" class="selectpicker" style="width: 100px" >
                                <c:choose>
                                    <c:when test="${tblProjectorGreens.plan == 0}"><!-- 如果 -->
                                        <option value="1"> 关 </option>
                                        <option selected="selected" value="0"> 开 </option>
                                    </c:when>
                                    <c:otherwise>
                                        <option selected="selected" value="1"> 关 </option>
                                        <option value="0"> 开 </option>
                                    </c:otherwise>
                                </c:choose>
                            </select>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <div style="height: auto;border: 0px;text-align: center" class="typeGreen">
                <shiro:hasPermission name="signal:tblColumn:edit">
                    <input id="btnSubmit"   class="btn btn-primary" type="submit" value="保 存" onclick="onSaveGreenProBtnClick()"/>&nbsp;
                </shiro:hasPermission>
                <shiro:hasPermission name="signal:tblColumn:put">
                    <a id="setBtn" onclick="setGreenPro()"  class="btn btn-primary" style="text-align: center">下 放</a>
                </shiro:hasPermission>
<%--                <a id="btnCancel" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>--%>
            </div>
        </div>
        <div  id="horn">
            <h1 style="text-align: center">音响配置</h1>
            <table id="contentHornTable" class="table table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th>时段类型</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>控制方案</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${tblHorns}" var="tblHorns">
                    <tr>
                        <td class="hide"><input id="tblHornsIds_${tblHorns.id}"  value="${tblHorns.id}" type="hidden"/></td>
                        <td>${tblHorns.planType == 0 ? "时段1": tblHorns.planType == 1 ? "时段2":"时段3"}</td>
                        <td>
                            <input id="tblHornsStartTime1_${tblHorns.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblHorns.startTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblHornsStartTime2_${tblHorns.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblHorns.startTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblHornsStartTime3_${tblHorns.id}" maxlength="2" class="input-width-20 type-second number" value="${tblHorns.startTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <input id="tblHornsEndTime1_${tblHorns.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblHorns.endTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblHornsEndTime2_${tblHorns.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblHorns.endTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblHornsEndTime3_${tblHorns.id}" maxlength="2" class="input-width-20 type-second number" value="${tblHorns.endTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <select id="tblHornsPlanSelect_${tblHorns.id}" class="selectpicker" style="width: 100px" >
                                <c:choose>
                                    <c:when test="${tblHorns.plan == 0}"><!-- 如果 -->
                                        <option value="1"> 关 </option>
                                        <option selected="selected" value="0"> 开 </option>
                                    </c:when>
                                    <c:otherwise>
                                        <option selected="selected" value="1"> 关 </option>
                                        <option value="0"> 开 </option>
                                    </c:otherwise>
                                </c:choose>
                            </select>
                        </td>
                    </tr>
                </c:forEach>

                </tbody>
            </table>
            <div style="height: auto;border: 0px;text-align: center"  >
                <shiro:hasPermission name="signal:tblColumn:edit">
                    <input id="btnSubmit"   class="btn btn-primary" type="submit" value="保 存" onclick="onSaveHornBtnClick()"/>&nbsp;
                </shiro:hasPermission>
                <shiro:hasPermission name="signal:tblColumn:put">
                    <a id="setBtn" onclick="setHorn()"  class="btn btn-primary">下 放</a>
                </shiro:hasPermission>
<%--                <a id="btnCancel" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>--%>
            </div>

        </div>
        <div  id="display">
            <h1 style="text-align: center">显示屏配置</h1>
            <table id="contentDislayTable" class="table table-striped table-bordered table-condensed">
                <thead>
                <tr>
                    <th>时段类型</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>控制方案</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${tblDisplays}" var="tblDisplays">
                    <tr>
                        <td class="hide"><input id="tblDisplayIds_${tblDisplays.id}"  value="${tblDisplays.id}" type="hidden"/></td>
                        <td>${tblDisplays.planType == 0 ? "时段1": tblDisplays.planType == 1 ? "时段2":"时段3"}</td>
                        <td>
                            <input id="tblDisplaysStartTime1_${tblDisplays.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblDisplays.startTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblDisplaysStartTime2_${tblDisplays.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblDisplays.startTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblDisplaysStartTime3_${tblDisplays.id}" maxlength="2" class="input-width-20 type-second number" value="${tblDisplays.startTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <input id="tblDisplaysEndTime1_${tblDisplays.id}" maxlength="2" class="input-width-20 type-hour number" value="${tblDisplays.endTime.split(':')[0]}"/> <b>:</b>
                            <input id="tblDisplaysEndTime2_${tblDisplays.id}" maxlength="2" class="input-width-20 type-minute number" value="${tblDisplays.endTime.split(':')[1]}"/> <b>:</b>
                            <input id="tblDisplaysEndTime3_${tblDisplays.id}" maxlength="2" class="input-width-20 type-second number" value="${tblDisplays.endTime.split(':')[2]}"/> </b>
                        </td>
                        <td>
                            <select id="tblDisplaysPlanSelect_${tblDisplays.id}" class="selectpicker" style="width: 100px" >
                                <c:choose>
                                    <c:when test="${tblDisplays.plan == 0}"><!-- 如果 -->
                                        <option value="1"> 开 </option>
                                        <option selected="selected" value="0"> 关 </option>
                                    </c:when>
                                    <c:otherwise>
                                        <option selected="selected" value="1"> 开 </option>
                                        <option value="0"> 关 </option>
                                    </c:otherwise>
                                </c:choose>
                            </select>
                        </td>
                    </tr>
                </c:forEach>

                </tbody>
            </table>
            <div style="height: auto;border: 0px;text-align: center"  >
                <shiro:hasPermission name="signal:tblColumn:edit">
                    <input id="btnSubmit"   class="btn btn-primary" type="submit" value="保 存" onclick="onSaveDisplayBtnClick()"/>&nbsp;
                </shiro:hasPermission>
                <shiro:hasPermission name="signal:tblColumn:put">
                    <a id="setBtn" onclick="setDisplay()"  class="btn btn-primary">下 放</a>
                </shiro:hasPermission>
<%--                <a id="btnCancel" class="btn" style="border: 1px solid #cccccc;" onclick="javascript:window.history.back();return false;">返 回</a>--%>
            </div>
        </div>
    </div>


</layout:default>