package com.lumlux.signal.entity;

/**
 * <p>Title: LightValueBean.java</p>
 * <p>Description: LightValueBean单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/5/27</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class LightValueBean {
    private String frequentGreen;//绿灯时长
    private String redTime;//红计时
    private String greenTime;//绿计时
    private String greenStartTime; // 绿灯开始时间以s为单位

    private String schemeNo;
    private String motoVehiGreenFlash;//机动绿闪
    private String safeGreen;//安全绿时
    private String yellowLight;//黄灯
    private String wholeRed;//全红
    private String cycleTotalLength;//信控周期
    private String  lightNumber;//灯号
    private int count;

    private int lightTotal;

    private int lightStatus;

    public int getLightStatus() {
        return lightStatus;
    }

    public void setLightStatus(int lightStatus) {
        this.lightStatus = lightStatus;
    }

    public int getLightTotal() {
        return lightTotal;
    }

    public void setLightTotal(int lightTotal) {
        this.lightTotal = lightTotal;
    }

    @Override
    public String toString() {
        return "LightValueBean{" +
                "frequentGreen='" + frequentGreen + '\'' +
                ", redTime='" + redTime + '\'' +
                ", greenTime='" + greenTime + '\'' +
                ", greenTime='" + greenStartTime + '\'' +
                ", SchemeNo='" + schemeNo + '\'' +
                ", motoVehiGreenFlash='" + motoVehiGreenFlash + '\'' +
                ", safeGreen='" + safeGreen + '\'' +
                ", yellowLight='" + yellowLight + '\'' +
                ", wholeRed='" + wholeRed + '\'' +
                ", cycleTotalLength='" + cycleTotalLength + '\'' +
                ", lightNo='" + lightNumber + '\'' +
                ", count=" + count +
                '}';
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getFrequentGreen() {
        return frequentGreen;
    }

    public void setFrequentGreen(String frequentGreen) {
        this.frequentGreen = frequentGreen;
    }

    public String getRedTime() {
        return redTime;
    }

    public void setRedTime(String redTime) {
        this.redTime = redTime;
    }

    public String getGreenTime() {
        return greenTime;
    }

    public void setGreenTime(String greenTime) {
        this.greenTime = greenTime;
    }

    public String getSchemeNo() {
        return schemeNo;
    }

    public void setSchemeNo(String schemeNo) {
        this.schemeNo = schemeNo;
    }

    public String getMotoVehiGreenFlash() {
        return motoVehiGreenFlash;
    }

    public void setMotoVehiGreenFlash(String motoVehiGreenFlash) {
        this.motoVehiGreenFlash = motoVehiGreenFlash;
    }

    public String getSafeGreen() {
        return safeGreen;
    }

    public void setSafeGreen(String safeGreen) {
        this.safeGreen = safeGreen;
    }

    public String getYellowLight() {
        return yellowLight;
    }

    public void setYellowLight(String yellowLight) {
        this.yellowLight = yellowLight;
    }

    public String getWholeRed() {
        return wholeRed;
    }

    public void setWholeRed(String wholeRed) {
        this.wholeRed = wholeRed;
    }

    public String getCycleTotalLength() {
        return cycleTotalLength;
    }

    public void setCycleTotalLength(String cycleTotalLength) {
        this.cycleTotalLength = cycleTotalLength;
    }

    public String getLightNumber() {
        return lightNumber;
    }

    public void setLightNumber(String lightNumber) {
        this.lightNumber = lightNumber;
    }

    public String getGreenStartTime() {
        return greenStartTime;
    }

    public void setGreenStartTime(String greenStartTime) {
        this.greenStartTime = greenStartTime;
    }
}
