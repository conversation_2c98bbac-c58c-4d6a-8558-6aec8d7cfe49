<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<groupId>signal</groupId>
	<artifactId>signal</artifactId>
	<version>1.0</version>
	<packaging>pom</packaging>
	<modules>
		<module>signal-qin</module>
		<module>signal-commons</module>
		<module>signal-admin</module>
		<module>signal-daemon</module>
	</modules>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<yangtes.version>1.0.71</yangtes.version>
		<app.copyright.year>2017</app.copyright.year>
		<app.upload.dir>D:/projects/adHoc/run/repository</app.upload.dir>
		<app.log.folder>D:/projects/adHoc/logs</app.log.folder>
		<app.ehcache.config.file>ehcache-local.xml</app.ehcache.config.file>
		<app.ehcache.dir>D:/projects/adHoc/run/ehcache</app.ehcache.dir>
		<database.jdbc.type>mysql</database.jdbc.type>
		<database.jdbc.driver>com.mysql.jdbc.Driver</database.jdbc.driver>
		<!-- 会为  -->
		<database.jdbc.url><![CDATA[**************************************************************************************************************]]></database.jdbc.url>
		<database.jdbc.name>signal_db</database.jdbc.name>
		<database.jdbc.password>signal_123_A</database.jdbc.password>
		<!-- 本地 -->
<!--		<database.jdbc.url><![CDATA[*******************************************************************************]]></database.jdbc.url>-->
<!--		<database.jdbc.name>root</database.jdbc.name>-->
<!--		<database.jdbc.password>root123456</database.jdbc.password>-->

		<database.jdbc.pool.init>1</database.jdbc.pool.init>
		<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>
		<database.jdbc.pool.max.active>20</database.jdbc.pool.max.active>
		<database.jdbc.test.sql><![CDATA[SELECT 1]]></database.jdbc.test.sql>
		<app.mina.service.ip>************</app.mina.service.ip>
		<app.mina.service.port>41120</app.mina.service.port>
		<!--		<app.mina.service.port>18080</app.mina.service.port>-->
		<app.mina.service.con>tcp</app.mina.service.con>

	</properties>
	<profiles>
		<profile>
			<id>sitDemo</id>
			<properties>
				<app.upload.dir>D:/projects/adHoc/run/repository</app.upload.dir>
				<app.log.folder>D:/projects/adHoc/logs</app.log.folder>
				<app.ehcache.dir>D:/projects/adHoc/run/ehcache</app.ehcache.dir>
				<!--<database.jdbc.url><![CDATA[*****************************************************************************]]></database.jdbc.url>-->
								<database.jdbc.url>****************************************************************************************************************************************</database.jdbc.url>
								<database.jdbc.name>signal_db</database.jdbc.name>
								<database.jdbc.password>signal_123_A</database.jdbc.password>
<!--				<database.jdbc.url>*********************************************************************************************************</database.jdbc.url>-->
<!--				<database.jdbc.name>root</database.jdbc.name>-->
<!--				<database.jdbc.password>root123456</database.jdbc.password>-->
				<database.jdbc.pool.init>1</database.jdbc.pool.init>
				<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>
				<database.jdbc.pool.max.active>20</database.jdbc.pool.max.active>
			</properties>
			<build>
				<finalName>signal</finalName>
			</build>
		</profile>
		<profile>
			<id>prd</id>
			<properties>
				<app.upload.dir>D:/projects/adHoc/run/repository</app.upload.dir>
				<app.log.folder>D:/projects/adHoc/logs`</app.log.folder>
				<app.ehcache.dir>D:/projects/adHoc/run/ehcache</app.ehcache.dir>
				<!--<database.jdbc.url><![CDATA[*****************************************************************************]]></database.jdbc.url>-->
								<database.jdbc.url>****************************************************************************************************************************************</database.jdbc.url>
								<database.jdbc.name>signal_db</database.jdbc.name>
								<database.jdbc.password>signal_123_A</database.jdbc.password>
<!--				<database.jdbc.url>*********************************************************************************************************</database.jdbc.url>-->
<!--				<database.jdbc.name>root</database.jdbc.name>-->
<!--				<database.jdbc.password>root123456</database.jdbc.password>-->
				<database.jdbc.pool.init>1</database.jdbc.pool.init>
				<database.jdbc.pool.min.idle>3</database.jdbc.pool.min.idle>
				<database.jdbc.pool.max.active>20</database.jdbc.pool.max.active>
				<app.mina.service.ip>************</app.mina.service.ip>
				<app.mina.service.port>41120</app.mina.service.port>
				<!--				<app.mina.service.port>18080</app.mina.service.port>-->
			</properties>
			<build>
				<finalName>signal</finalName>
			</build>
		</profile>
	</profiles>
	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<executions>
					<execution>
						<id>my-jar</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>api</classifier>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>

		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>1.7</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-clean-plugin</artifactId>
					<version>2.6.1</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.3</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<version>2.6</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-install-plugin</artifactId>
					<version>2.5.2</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>2.6</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-source-plugin</artifactId>
					<version>2.4</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-war-plugin</artifactId>
					<version>2.1.1</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>
				<plugin>
					<groupId>org.apache.tomcat.maven</groupId>
					<artifactId>tomcat7-maven-plugin</artifactId>
					<version>2.2</version>
				</plugin>
			</plugins>
		</pluginManagement>

	</build>
</project>