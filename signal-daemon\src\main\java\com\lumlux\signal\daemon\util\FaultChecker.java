package com.lumlux.signal.daemon.util;

/**
 * <p>Title: FaultChecker.java</p >
 * <p>Description: FaultChecker单表表单</p >
 * <p>author: JiaKing</p >
 * <p>Date: 2024/12/3</p >
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p >
 */

public class FaultChecker {


    // 方法用于检测功率连续10次为0的情况，并返回相应的字符串
    public static String detectFault(String[] powerValues) {
        // 定义连续零的计数器
        int zeroCount = 0;
        // 定义连续零的阈值
        final int FAULT_THRESHOLD = 15;

        // 遍历功率数组
        for (String power : powerValues) {
            // 尝试将字符串转换为整数
            try {
                int powerValue = Integer.parseInt(power);
                // 如果功率为0，则增加计数器
                if (powerValue == 0) {
                    zeroCount++;
                    // 如果计数器达到阈值，则认为是故障
                    if (zeroCount >= FAULT_THRESHOLD) {
                        return "2";
                    }
                } else {
                    // 如果功率不为0，则重置计数器
                    zeroCount = 0;
                }
            } catch (NumberFormatException e) {
                // 如果字符串不能转换为整数，重置计数器并继续
                zeroCount = 0;
                System.out.println("输入数据包含无效的数字格式：" + power);
            }
        }
        // 如果遍历结束后没有达到故障阈值，则认为不是故障
        return "1";
    }

    public static void main(String[] args) {
        // 示例数据
        String[] powerValues = {"0", "0", "0", "0", "0", "0", "0", "0", "0", "0"};

        // 调用detectFault方法并输出结果
        String result = detectFault(powerValues);
        System.out.println(result);
    }
}
