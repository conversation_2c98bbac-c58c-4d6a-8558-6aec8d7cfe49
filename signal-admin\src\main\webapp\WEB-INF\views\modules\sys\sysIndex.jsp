<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8"%>

<%@ include file="/WEB-INF/views/include/taglib.jsp"%>

<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld"%>
<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld"%>
<layout:topcentre title="${fns:getConfig('productName')}">
	<style type="text/css">
		body {
			width: 100%;
			height: 100%;
			margin: 0;
			padding: 0;
			overflow: hidden;
			position: relative;
			background: #f0f4fd;
		}

		html {
			height: 100%;
		}

		.navbar-brand {
			flex: 1;
		}

		.navbar-brand {
			flex: 1;
			float: none;
		}

		#footer {
			height: 30px;
		}

		#footer, #footer a {
			color: #999;
			z-index:2;
		}

		#footer a:hover {
			text-decoration: none;
			color: #337ab7;
		}

		.modal-content {
			position: relative;
			background-color: #fff;
			-webkit-background-clip: padding-box;
			background-clip: padding-box;
			border: 1px solid #999;
			border: 1px solid rgba(0,0,0,.2);
			border-radius: 6px;
			outline: 0;
			-webkit-box-shadow: 0 3px 9px rgba(0,0,0,.5);
			box-shadow: 0 3px 9px rgba(0,0,0,.5);
		}

		.modal{
			width:auto;
			margin-left:0;
			background-color:transparent !important;
			border:0;
		}

		.modal-backdrop {
			background-color: #000;
		}

		@media (min-width: 768px)
		{
			.modal-dialog {
				width: 800px;
				margin: 30px auto;
			}
		}

		.modal-footer {
			padding: 15px;
			text-align: right;
			border-top: 1px solid #e5e5e5;
			border-radius: 6px;
		}

		.navList {
			margin: auto;
			width: 900px;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, calc(-50% + 26px));
		}

		.navRow {
			font-size: 0px;
			clear: both;
			margin-bottom: 10px;
		}

		.navDiv {
			width: 180px;
			height: 180px;
			display: inline-flex;
			float: inherit;
			user-select: none;
			-webkit-user-drag: none;
		}

		.navBigImg {
			width: 360px;
		}

		.navItem {
			background: #47afea;
			position: relative;
			cursor: pointer;

		}

		.navItem >img {
			height: 55px;
			margin: auto;
			padding-bottom: 15px;
			box-sizing: content-box;
		}

		.navItem >p {
			position: absolute;
			width: 100%;
			text-align: center;
			font-size: 20px;
			color: white;
			bottom: 0;
			margin: 20px 0;
		}


	</style>
	<script src="${ctxStatic}/bootstrap/js/jquery-3.3.1.js"></script>
	<script src="${ctxStatic}/bootstrap/js/bootstrap-3.3.7.js"></script>
	<script src="${ctxStatic}/common/markdown.js"></script>
	<div class="navList" id="navList">
		<div class="navRow" style="float:left">
			<shiro:hasPermission name="signal:tbladHoc:view">
				<div class="navDiv navItem" onclick="navigate2Frame('adHoc')">
					<img src="${ctxStatic}/image/icon9.png">
					<p>设备管理</p>
					<c:set var="adhoc" value="1"></c:set>
				</div>
			</shiro:hasPermission>
			<c:if test="${empty adhoc}">
				<div class="navDiv navItem" style="cursor: not-allowed;">
					<img src="${ctxStatic}/image/icon9.png">
					<p>设备管理</p>
				</div>
			</c:if>

			<img class="navDiv" src="${ctxStatic}/image/pic1.png">

			<shiro:hasPermission name="signal:gismap:view">
				<div class="navDiv navItem" onclick="navigate2NewWindow('gis.html')">
					<img src="${ctxStatic}/image/icon1.png">
					<p>GIS地图</p>
					<c:set var="gismap" value="1"></c:set>
				</div>
			</shiro:hasPermission>
			<c:if test="${empty gismap}">
				<div class="navDiv navItem" style="cursor: not-allowed;">
					<img src="${ctxStatic}/image/icon1.png">
					<p>GIS地图</p>
				</div>
			</c:if>

			<img class="navDiv" src="${ctxStatic}/image/pic2.png">
		</div>

		<div class="navRow" style="float:right">
			<img class="navDiv" src="${ctxStatic}/image/pic8.png">

			<shiro:hasAnyPermissions name="sys:area:view,sys:dict:view,sys:log:view">
				<div class="navDiv navItem" onclick="navigate2Frame('Sys')">
					<img src="${ctxStatic}/image/icon2.png">
					<p>系统管理</p>
					<c:set var="system" value="1" />
				</div>
			</shiro:hasAnyPermissions>
			<c:if test="${empty system}">
				<div class="navDiv navItem" style="cursor: not-allowed;">
					<img src="${ctxStatic}/image/icon2.png">
					<p>系统管理</p>
				</div>
			</c:if>

			<img class="navDiv" src="${ctxStatic}/image/pic9.png">

			<shiro:hasAnyPermissions name="signal:tblSupport:view,signal:mgrOffice:view,signal:tblRepairOrder:view">
				<div class="navDiv navItem" onclick="navigate2Frame('repair')">
					<img src="${ctxStatic}/image/icon3.png">
					<p>运维管理</p>
					<c:set var="support" value="1" />
				</div>
			</shiro:hasAnyPermissions>
			<c:if test="${empty support}">
				<div class="navDiv navItem" style="cursor: not-allowed;">
					<img src="${ctxStatic}/image/icon3.png">
					<p>运维管理</p>
				</div>
			</c:if>
		</div>
	</div>
	<div id="footer" class="row-fluid" style="position: fixed; bottom: 0; left: 0; text-align: center; border-top: 2px solid #47afea; padding: 5px 0; font-size: 11px;">
		<div style="float: left; margin-left: 20px;">版权所有	${fns:getDictLabel('name', 'office_info', '未知企业')}</div>
		<a href style="display: block; float: left; margin-left: 10px;"	data-toggle='modal' data-target='#versionModal'>${fns:getDictLabel('name', 'version', 'v1.0')}</a>
		<div style="float: right; margin-right: 20px;">总机 :	${fns:getDictLabel('phone', 'office_info', '暂无')} <%--传真 :	${fns:getDictLabel('fax', 'office_info', '暂无')}--%></div>
	</div>
	<div class="modal fade" id="versionModal" tabindex="-1" role="dialog" aria-labelledby="versionModal" aria-hidden="true" style="overflow-y:hidden;">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
					<h3 class="modal-title">版本更新</h3>
				</div>
				<div class="modal-body">
					1.xxx
					2.xxx
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>
	<script>
		$().ready(function() {
			var flag = "${empty collector}";
			var pa = "${collector}";
			doPost("a/signal/tblCollector/isFrozen", "", function(result) {
				if(result.data!=null) {
					alert(result.data);
					doPost("a/signal/tblCollector/userFrozen", "", function(result) {
						window.location.href = "a/login";
					});
				}
			});
			doPost("a/signal/tblCollector/session", "", function(result) {

			});
		});

		var base = window.location.href.substring(0, window.location.href.lastIndexOf("/"));
		var navigate2Frame = target => {
			window.location.href = base + '/theme/flat?url=' + base + '/entry.jsp?param=' + target;
		}

		var navigate2NewWindow = target => {
			window.open("/adHoc/" + target);
		}

		var showVersion = function() {
			doPost("a/signal/tblCollector/showVersion", "", function(result) {
				if (result.status == "200") {
					//$(".modal-body").html("<pre>" +  markdown.toHTML('#hello markdown!') + "</pre>");
					$(".modal-body").html("<pre>" +  markdown.toHTML(result.data) + "</pre>");
				}
			});
		}

		var doPost = function(url, jsonDate, callback) {
			$.ajax({
				type : "POST",
				url : url,
				data : JSON.stringify(jsonDate),
				contentType : "application/json; charset=utf-8",
				dataType : "json",
				success : callback,
				error : function(msg) {
					/* console.log(msg); */
				}
			});
		}

		$('#versionModal').on('shown.bs.modal', function() {
			showVersion();
		});

		$('#versionModal').on('hidden.bs.modal', function() {
			$('body')[0].style.paddingRight = 0;
		});

		var baseTransform = "translate(-50%, calc(-50% + 26px)) ";
		var onWindowResize = _ => $("#navList").css("transform", baseTransform + "scale(" + Math.min(window.innerWidth / 1920 * 1.3, window.innerHeight / 960) + ")");
		window.onresize = _ =>  onWindowResize();
		onWindowResize();
	</script>
</layout:topcentre>