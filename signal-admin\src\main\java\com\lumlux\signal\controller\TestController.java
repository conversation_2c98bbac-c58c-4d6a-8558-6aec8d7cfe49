package com.lumlux.signal.controller;

import com.heinqi.yangtes.base.web.AsyncResponseData;
import com.heinqi.yangtes.jee.commons.web.BaseController;
import com.lumlux.commons.entity.*;
import com.lumlux.pd.controller.LoginController;
import com.lumlux.signal.controller.datashow.EchartsController;
import com.lumlux.signal.service.DaemonService;
import com.lumlux.signal.service.TblCollectorService;
import com.lumlux.signal.service.TblVariableConfigService;
import com.lumlux.signal.service.TblVariableService;
import com.lumlux.signal.util.Constant;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



@Controller

@RequestMapping(value = "/test")
public class TestController extends BaseController {


    @Autowired
    private DaemonService daemonService;

    @Autowired
    private TblVariableService variableService;

    @Autowired
    private TblVariableConfigService variableConfigService;




    @RequestMapping(value = "putRoute", method = RequestMethod.POST)
    @ResponseBody
    public AsyncResponseData.ResultData putRoute(@RequestBody RouterBean routerBean) {
        logger.info("TestController putRoute : 开始下放测试通道信息");
        AsyncResponseData.ResultData data = AsyncResponseData.get200Response();
        Map<String, String> messages = new HashMap<>();
        String hostName =null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
        if (null == routerBean.getVariableId()){
            data.setStatus(400);
            messages.put("message", "可变车道控制器id不能为空");
            data.setMessages(messages);
            return data;
        }

        int controlType = routerBean.getControlType();
        String controlTypeStr = String.valueOf(controlType);
        if (!controlTypeStr.matches("[1-9]")) {
            data.setStatus(400);
            messages.put("message", "通道类型必须输入1-9的整数(1-7：星期几，8：手动控制(手动开关)，8：手动控制(手动开关)，9：紧急控制(紧急开关))不能为空");
            data.setMessages(messages);
            return data;
        }


        String variableId = routerBean.getVariableId();
        TblVariable tblVariable = variableService.get(variableId);
        if (tblVariable != null) {
            List<RouterParam> routerParamLists = routerBean.getRouterParam();

            if (0 == routerParamLists.size() ){
                data.setStatus(400);
                messages.put("message", "下放通道配置参数不能为空");
                data.setMessages(messages);
                return data;
            }else if (routerParamLists.size() > 0) { //9 星期
                try {
//                    daemonService.putConfigTest(tblVariable.getCode(), routerBean);
                    daemonService.putConfigTestThree(tblVariable.getCode(), routerBean);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                for (int i = 0; i < routerParamLists.size(); i++) {//第几个方案 ,+1
                    String iStr = String.valueOf(i);

                    if (!iStr.matches("[0-2]")) {
                        data.setStatus(400);
                        messages.put("message", "方案类型必须输入0-2的整数");
                        data.setMessages(messages);
                        return data;
                    }
                    RouterParam routerParam = routerParamLists.get(i);//通道

                    TblVariableScheme variableScheme = new TblVariableScheme();

                    if (null == routerParam || null== routerParam.getStartTime()   || null == routerParam.getEndTime()  || 0 == routerParam.getEnable().size() ){
                        data.setStatus(400);
                        messages.put("message", "下放通道配置参数必须完整的给值");
                        data.setMessages(messages);
                        return data;
                    }else if (routerParam != null && routerParam.getStartTime() != null && routerParam.getEndTime() != null && routerParam.getEnable().size() > 0) {
                        if (Constant.ERROR_CODE.equals(routerParam.getStartTime()) && Constant.ERROR_CODE.equals(routerParam.getEndTime())) {
                            variableScheme.setSchemeEndTime(null);
                            variableScheme.setSchemeStartTime(null);
                            variableScheme.setVariableId(variableId);
//                            variableScheme.setCreateBy(tblVariable.getUpdateBy());
                            variableScheme.setUpdateBy(hostName);

//                            variableScheme.setCreateDate(new Date());
                            variableScheme.setUpdateDate(new Date());
                            variableScheme.setControlType(String.valueOf(routerBean.getControlType()));
                            variableScheme.setType(String.valueOf(i));
                            variableScheme.setEnabled("0");
                            variableConfigService.updateSchemeParamErrorCode(variableScheme, variableId);
                        } else {

                            variableScheme.setSchemeStartTime(routerParam.getStartTime());
                            variableScheme.setSchemeEndTime(routerParam.getEndTime());
                            variableScheme.setVariableId(variableId);
//                            variableScheme.setCreateBy(tblVariable.getUpdateBy());
                            variableScheme.setUpdateBy(hostName);

//                            variableScheme.setCreateDate(new Date());
                            variableScheme.setUpdateDate(new Date());
                            variableScheme.setControlType(String.valueOf(routerBean.getControlType()));
                            variableScheme.setType(String.valueOf(i));
                            variableScheme.setEnabled("1");
                            variableConfigService.updateSchemeParam(variableScheme, variableId);
                        }
                        List<Integer> enables = routerParam.getEnable();

                        //i指的是周几 //指的是方案几
                        String variableSchemeId = variableConfigService.findVariableSchemeId(variableId,String.valueOf(controlType),String.valueOf(i));
                        if (null != variableSchemeId) {
                            Map<Integer,Integer> maps = new HashMap<>();
                            for (int k = 0; k < enables.size(); k++) {
                                maps.put(k,enables.get(k));
                                String  id = variableConfigService.findVariableRouterParam(variableId,variableSchemeId,String.valueOf(controlType),String.valueOf(i),k+1);
                                if (null != id) {

                                    variableConfigService.updateRouterParam(id,k+1,enables.get(k),hostName);
                                }
                            }
                        }
                    }
                }
            }
            messages.put("message", "下发成功");
            data.setMessages(messages);
            data.setStatus(AsyncResponseData.RESPONSE_STATUS_OK);
            logger.info("TblVariableConfigController putRoute :下放通道信息结束");
            return data;
        }
        return null;
    }


}
