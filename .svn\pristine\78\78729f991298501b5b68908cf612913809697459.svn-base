package com.lumlux.signal.util;

/**
 * <p>Title: NumberUtils.java</p>
 * <p>Description: NumberUtils单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2023/10/17</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class NumberUtils {

    public static boolean isBlankOrLess(final Double cs) {
        return cs == null || cs < 0;

    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
    public static byte mergeNibbles(int highNibble, int lowNibble) {
        // 确保输入在 4 位范围内 (0-15)
        highNibble = highNibble & 0xF;
        lowNibble = lowNibble & 0xF;

        // 左移高四位并与低四位按位或
        return (byte) ((highNibble << 4) | lowNibble);
    }
}
