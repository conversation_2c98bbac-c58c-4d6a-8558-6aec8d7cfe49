# FunctionList
FUNCTIONLIST=FunctionList
REALPLAY=RealPlay
CAPTURE_PICTURE=Capture Picture
MULTIREALPLAY=MultiRealPlay
DOWNLOAD_RECORD=Download Record
ITS_EVENT=TrafficEvent
TALK=Talk
DEVICESEARCH_DEVICEINIT=DeviceSearchAndInit
PTZ=PTZ Control
FACERECOGNITION=FaceRecognition
ALARM_LISTEN=Alarm Listen
DEVICE_CONTROL=Device Control
AUTOREGISTER=AutoRegister
REMOTE_OPEN_DOOR=Remote Open Door

ONLINE=Online

# Login Info
DEVICE_IP=Device IP
DEVICE_PORT=Port
USERNAME=UserName
PASSWORD=Password
IP=IP

LOGIN=Login
LOGOUT=Logout

LOGIN_SUCCEED=Login Succeed
LOGIN_FAILED=Login Failed
DISCONNECT=Device disconnect
DISCONNECT_RECONNECTING=Device disconnect, reconnecting
PROMPT_MESSAGE=Prompt Message
ERROR_MESSAGE=Error Message

SUCCEED=Succeed
FAILED=Failed

PLEASE_INPUT_DEVICE_IP=Please input device IP
PLEASE_INPUT_DEVICE_PORT=Please input device port
PLEASE_INPUT_DEVICE_USERNAME=Please input userName
PLEASE_INPUT_DEVICE_PASSWORD=Please input password
PLEASE_INPUT_CONFIRM_PASSWORD=Please input confirm password

# Error Info
NET_NOERROR=No error 
NET_ERROR=Unknown error
NET_SYSTEM_ERROR=System error
NET_NETWORK_ERROR=Protocol error it may result from network timeout
NET_DEV_VER_NOMATCH=Device protocol does not match 
NET_INVALID_HANDLE=Handle is invalid
NET_OPEN_CHANNEL_ERROR=Failed to open channel 
NET_CLOSE_CHANNEL_ERROR=Failed to close channel 
NET_ILLEGAL_PARAM=User parameter is illegal 
NET_SDK_INIT_ERROR=SDK initialization error 
NET_SDK_UNINIT_ERROR=SDK clear error 
NET_RENDER_OPEN_ERROR=Error occurs when apply for render resources
NET_DEC_OPEN_ERROR=Error occurs when opening the decoder library 
NET_DEC_CLOSE_ERROR=Error occurs when closing the decoder library 
NET_MULTIPLAY_NOCHANNEL=The detected channel number is 0 in multiple-channel preview 
NET_TALK_INIT_ERROR=Failed to initialize record library 
NET_TALK_NOT_INIT=The record library has not been initialized
NET_TALK_SENDDATA_ERROR=Error occurs when sending out audio data 
NET_REAL_ALREADY_SAVING=The real-time has been protected
NET_NOT_SAVING=The real-time data has not been save
NET_OPEN_FILE_ERROR=Error occurs when opening the file
NET_PTZ_SET_TIMER_ERROR=Failed to enable PTZ to control timer
NET_RETURN_DATA_ERROR=Error occurs when verify returned data
NET_INSUFFICIENT_BUFFER=There is no sufficient buffer
NET_NOT_SUPPORTED=The current SDK does not support this funcntion
NET_NO_RECORD_FOUND=There is no searched result
NET_NOT_AUTHORIZED=You have no operation right
NET_NOT_NOW=Can not operate right now 
NET_NO_TALK_CHANNEL=There is no audio talk channel
NET_NO_AUDIO=There is no audio
NET_NO_INIT=The network SDK has not been initialized
NET_DOWNLOAD_END=The download completed
NET_EMPTY_LIST=There is no searched result
NET_ERROR_GETCFG_SYSATTR=Failed to get system property setup
NET_ERROR_GETCFG_SERIAL=Failed to get SN
NET_ERROR_GETCFG_GENERAL=Failed to get general property
NET_ERROR_GETCFG_DSPCAP=Failed to get DSP capacity description
NET_ERROR_GETCFG_NETCFG=Failed to get network channel setup
NET_ERROR_GETCFG_CHANNAME=Failed to get channel name
NET_ERROR_GETCFG_VIDEO=Failed to get video property
NET_ERROR_GETCFG_RECORD=Failed to get record setup
NET_ERROR_GETCFG_PRONAME=Failed to get decoder protocol name 
NET_ERROR_GETCFG_FUNCNAME=Failed to get 232 COM function name
NET_ERROR_GETCFG_485DECODER=Failed to get decoder property
NET_ERROR_GETCFG_232COM=Failed to get 232 COM setup
NET_ERROR_GETCFG_ALARMIN=Failed to get external alarm input setup
NET_ERROR_GETCFG_ALARMDET=Failed to get motion detection alarm
NET_ERROR_GETCFG_SYSTIME=Failed to get device time
NET_ERROR_GETCFG_PREVIEW=Failed to get preview parameter
NET_ERROR_GETCFG_AUTOMT=Failed to get audio maintenance setup
NET_ERROR_GETCFG_VIDEOMTRX=Failed to get video matrix setup
NET_ERROR_GETCFG_COVER=Failed to get privacy mask zone setup
NET_ERROR_GETCFG_WATERMAKE=Failed to get video watermark setup
NET_ERROR_GETCFG_MULTICAST=Failed to get config, omulticast port by channel
NET_ERROR_SETCFG_GENERAL=Failed to modify general property
NET_ERROR_SETCFG_NETCFG=Failed to modify channel setup
NET_ERROR_SETCFG_CHANNAME=Failed to modify channel name
NET_ERROR_SETCFG_VIDEO=Failed to modify video channel 
NET_ERROR_SETCFG_RECORD=Failed to modify record setup 
NET_ERROR_SETCFG_485DECODER=Failed to modify decoder property 
NET_ERROR_SETCFG_232COM=Failed to modify 232 COM setup 
NET_ERROR_SETCFG_ALARMIN=Failed to modify external input alarm setup
NET_ERROR_SETCFG_ALARMDET=Failed to modify motion detection alarm setup 
NET_ERROR_SETCFG_SYSTIME=Failed to modify device time 
NET_ERROR_SETCFG_PREVIEW=Failed to modify preview parameter
NET_ERROR_SETCFG_AUTOMT=Failed to modify auto maintenance setup 
NET_ERROR_SETCFG_VIDEOMTRX=Failed to modify video matrix setup 
NET_ERROR_SETCFG_COVER=Failed to modify privacy mask zone
NET_ERROR_SETCFG_WATERMAKE=Failed to modify video watermark setup 
NET_ERROR_SETCFG_WLAN=Failed to modify wireless network information 
NET_ERROR_SETCFG_WLANDEV=Failed to select wireless network device
NET_ERROR_SETCFG_REGISTER=Failed to modify the actively registration parameter setup
NET_ERROR_SETCFG_CAMERA=Failed to modify camera property
NET_ERROR_SETCFG_INFRARED=Failed to modify IR alarm setup
NET_ERROR_SETCFG_SOUNDALARM=Failed to modify audio alarm setup
NET_ERROR_SETCFG_STORAGE=Failed to modify storage position setup
NET_AUDIOENCODE_NOTINIT=The audio encode port has not been successfully initialized 
NET_DATA_TOOLONGH=The data are too long
NET_UNSUPPORTED=The device does not support current operation 
NET_DEVICE_BUSY=Device resources is not sufficient
NET_SERVER_STARTED=The server has boot up 
NET_SERVER_STOPPED=The server has not fully boot up 
NET_LISTER_INCORRECT_SERIAL=Input serial number is not correct
NET_QUERY_DISKINFO_FAILED=Failed to get HDD information
NET_ERROR_GETCFG_SESSION=Failed to get connect session information
NET_USER_FLASEPWD_TRYTIME=The password you typed is incorrect You have exceeded the maximum number of retries
NET_LOGIN_ERROR_PASSWORD=Password is not correct
NET_LOGIN_ERROR_USER=The account does not exist
NET_LOGIN_ERROR_TIMEOUT=Time out for log in returned value
NET_LOGIN_ERROR_RELOGGIN=The account has logged in 
NET_LOGIN_ERROR_LOCKED=The account has been locked
NET_LOGIN_ERROR_BLACKLIST=The account has been in the black list
NET_LOGIN_ERROR_BUSY=Resources are not sufficient System is busy now
NET_LOGIN_ERROR_CONNECT=Time out Please check network and try again
NET_LOGIN_ERROR_NETWORK=Network connection failed
NET_LOGIN_ERROR_SUBCONNECT=Successfully logged in the device but can not create video channel Please check network connection
NET_LOGIN_ERROR_MAXCONNECT=Exceed the max connect number
NET_LOGIN_ERROR_PROTOCOL3_ONLY=Only support 3 protocol
NET_LOGIN_ERROR_UKEY_LOST=There is no USB or USB info error
NET_LOGIN_ERROR_NO_AUTHORIZED=Client-end IP address has no right to login
NET_LOGIN_ERROR_USER_OR_PASSOWRD=user or password error 
NET_LOGIN_ERROR_DEVICE_NOT_INIT=cannot login because the device has not been init,please init the device and then login
NET_RENDER_SOUND_ON_ERROR=Error occurs when Render library open audio
NET_RENDER_SOUND_OFF_ERROR=Error occurs when Render library close audio 
NET_RENDER_SET_VOLUME_ERROR=Error occurs when Render library control volume
NET_RENDER_ADJUST_ERROR=Error occurs when Render library set video parameter
NET_RENDER_PAUSE_ERROR=Error occurs when Render library pause play
NET_RENDER_SNAP_ERROR=Render library snapshot error
NET_RENDER_STEP_ERROR=Render library stepper error
NET_RENDER_FRAMERATE_ERROR=Error occurs when Render library set frame rate
NET_RENDER_DISPLAYREGION_ERROR=Error occurs when Render lib setting show region
NET_RENDER_GETOSDTIME_ERROR=An error occurred when Render library getting current play time
NET_GROUP_EXIST=Group name has been existed
NET_GROUP_NOEXIST=The group name does not exist 
NET_GROUP_RIGHTOVER=The group right exceeds the right list!
NET_GROUP_HAVEUSER=The group can not be removed since there is user in it!
NET_GROUP_RIGHTUSE=The user has used one of the group right It can not be removed 
NET_GROUP_SAMENAME=New group name has been existed
NET_USER_EXIST=The user name has been existed
NET_USER_NOEXIST=The account does not exist
NET_USER_RIGHTOVER=User right exceeds the group right 
NET_USER_PWD=Reserved account It does not allow to be modified
NET_USER_FLASEPWD=password is not correct
NET_USER_NOMATCHING=Password is invalid
NET_USER_INUSE=account in use
NET_ERROR_GETCFG_ETHERNET=Failed to get network card setup
NET_ERROR_GETCFG_WLAN=Failed to get wireless network information
NET_ERROR_GETCFG_WLANDEV=Failed to get wireless network device
NET_ERROR_GETCFG_REGISTER=Failed to get actively registration parameter
NET_ERROR_GETCFG_CAMERA=Failed to get camera property 
NET_ERROR_GETCFG_INFRARED=Failed to get IR alarm setup
NET_ERROR_GETCFG_SOUNDALARM=Failed to get audio alarm setup
NET_ERROR_GETCFG_STORAGE=Failed to get storage position 
NET_ERROR_GETCFG_MAIL=Failed to get mail setup
NET_CONFIG_DEVBUSY=Can not set right now 
NET_CONFIG_DATAILLEGAL=The configuration setup data are illegal
NET_ERROR_GETCFG_DST=Failed to get DST setup
NET_ERROR_SETCFG_DST=Failed to set DST 
NET_ERROR_GETCFG_VIDEO_OSD=Failed to get video OSD setup
NET_ERROR_SETCFG_VIDEO_OSD=Failed to set video OSD 
NET_ERROR_GETCFG_GPRSCDMA=Failed to get CDMA\GPRS configuration
NET_ERROR_SETCFG_GPRSCDMA=Failed to set CDMA\GPRS configuration
NET_ERROR_GETCFG_IPFILTER= Failed to get IP Filter configuration
NET_ERROR_SETCFG_IPFILTER=Failed to set IP Filter configuration
NET_ERROR_GETCFG_TALKENCODE=Failed to get Talk Encode configuration
NET_ERROR_SETCFG_TALKENCODE=Failed to set Talk Encode configuration
NET_ERROR_GETCFG_RECORDLEN=Failed to get The length of the video package configuration
NET_ERROR_SETCFG_RECORDLEN=Failed to set The length of the video package configuration
NET_DONT_SUPPORT_SUBAREA=Not support Network hard disk partition
NET_ERROR_GET_AUTOREGSERVER=Failed to get the register server information
NET_ERROR_CONTROL_AUTOREGISTER=Failed to control actively registration
NET_ERROR_DISCONNECT_AUTOREGISTER=Failed to disconnect actively registration
NET_ERROR_GETCFG_MMS=Failed to get mms configuration
NET_ERROR_SETCFG_MMS=Failed to set mms configuration
NET_ERROR_GETCFG_SMSACTIVATION=Failed to get SMS configuration
NET_ERROR_SETCFG_SMSACTIVATION=Failed to set SMS configuration
NET_ERROR_GETCFG_DIALINACTIVATION=Failed to get activation of a wireless connection
NET_ERROR_SETCFG_DIALINACTIVATION=Failed to set activation of a wireless connection
NET_ERROR_GETCFG_VIDEOOUT=Failed to get the parameter of video output
NET_ERROR_SETCFG_VIDEOOUT=Failed to set the configuration of video output
NET_ERROR_GETCFG_OSDENABLE=Failed to get osd overlay enabling
NET_ERROR_SETCFG_OSDENABLE=Failed to set OSD overlay enabling
NET_ERROR_SETCFG_ENCODERINFO=Failed to set digital input configuration of front encoders
NET_ERROR_GETCFG_TVADJUST=Failed to get TV adjust configuration
NET_ERROR_SETCFG_TVADJUST=Failed to set TV adjust configuration
NET_ERROR_CONNECT_FAILED=Failed to request to establish a connection
NET_ERROR_SETCFG_BURNFILE=Failed to request to upload burn files
NET_ERROR_SNIFFER_GETCFG=Failed to get capture configuration information
NET_ERROR_SNIFFER_SETCFG=Failed to set capture configuration information
NET_ERROR_DOWNLOADRATE_GETCFG=Failed to get download restrictions information
NET_ERROR_DOWNLOADRATE_SETCFG=Failed to set download restrictions information
NET_ERROR_SEARCH_TRANSCOM=Failed to query serial port parameters
NET_ERROR_GETCFG_POINT=Failed to get the preset info
NET_ERROR_SETCFG_POINT=Failed to set the preset info
NET_SDK_LOGOUT_ERROR=SDK log out the device abnormally
NET_ERROR_GET_VEHICLE_CFG=Failed to get vehicle configuration
NET_ERROR_SET_VEHICLE_CFG=Failed to set vehicle configuration
NET_ERROR_GET_ATM_OVERLAY_CFG=Failed to get ATM overlay configuration
NET_ERROR_SET_ATM_OVERLAY_CFG=Failed to set ATM overlay configuration
NET_ERROR_GET_ATM_OVERLAY_ABILITY=Failed to get ATM overlay ability
NET_ERROR_GET_DECODER_TOUR_CFG=Failed to get decoder tour configuration
NET_ERROR_SET_DECODER_TOUR_CFG=Failed to set decoder tour configuration
NET_ERROR_CTRL_DECODER_TOUR=Failed to control decoder tour
NET_GROUP_OVERSUPPORTNUM=Beyond the device supports for the largest number of user groups
NET_USER_OVERSUPPORTNUM=Beyond the device supports for the largest number of users 
NET_ERROR_GET_SIP_CFG=Failed to get SIP configuration
NET_ERROR_SET_SIP_CFG=Failed to set SIP configuration
NET_ERROR_GET_SIP_ABILITY=Failed to get SIP capability
NET_ERROR_GET_WIFI_AP_CFG=Failed to get "WIFI ap' configuration 
NET_ERROR_SET_WIFI_AP_CFG=Failed to set "WIFI ap" configuration  
NET_ERROR_GET_DECODE_POLICY=Failed to get decode policy 
NET_ERROR_SET_DECODE_POLICY=Failed to set decode policy 
NET_ERROR_TALK_REJECT=refuse talk
NET_ERROR_TALK_OPENED=talk has opened by other client
NET_ERROR_TALK_RESOURCE_CONFLICIT=resource conflict
NET_ERROR_TALK_UNSUPPORTED_ENCODE=unsupported encode type
NET_ERROR_TALK_RIGHTLESS=no right
NET_ERROR_TALK_FAILED=request failed
NET_ERROR_GET_MACHINE_CFG=Failed to get device relative config
NET_ERROR_SET_MACHINE_CFG=Failed to set device relative config
NET_ERROR_GET_DATA_FAILED=get data failed
NET_ERROR_MAC_VALIDATE_FAILED=MAC validate failed
NET_ERROR_GET_INSTANCE=Failed to get server instance 
NET_ERROR_JSON_REQUEST=Generated json string is error
NET_ERROR_JSON_RESPONSE=The responding json string is error
NET_ERROR_VERSION_HIGHER=The protocol version is lower than current version
NET_SPARE_NO_CAPACITY=Hotspare disk operation failed The capacity is low
NET_ERROR_SOURCE_IN_USE=Display source is used by other output
NET_ERROR_REAVE=advanced users grab low-level user resource
NET_ERROR_NETFORBID=net forbid
NET_ERROR_GETCFG_MACFILTER=get MAC filter configuration error
NET_ERROR_SETCFG_MACFILTER=set MAC filter configuration error
NET_ERROR_GETCFG_IPMACFILTER=get IP/MAC filter configuration error
NET_ERROR_SETCFG_IPMACFILTER=set IP/MAC filter configuration error
NET_ERROR_OPERATION_OVERTIME=operation over time 
NET_ERROR_SENIOR_VALIDATE_FAILED=senior validation failure
NET_ERROR_DEVICE_ID_NOT_EXIST=device ID is not exist
NET_ERROR_UNSUPPORTED=unsupport operation
NET_ERROR_PROXY_DLLLOAD=proxy dll load error
NET_ERROR_PROXY_ILLEGAL_PARAM= proxy user parameter is not legal
NET_ERROR_PROXY_INVALID_HANDLE=handle invalid
NET_ERROR_PROXY_LOGIN_DEVICE_ERROR=login device error
NET_ERROR_PROXY_START_SERVER_ERROR=start proxy server error
NET_ERROR_SPEAK_FAILED=request speak failed
NET_ERROR_NOT_SUPPORT_F6=unsupport F6
NET_ERROR_CD_UNREADY=CD is not ready
NET_ERROR_DIR_NOT_EXIST=Directory does not exist
NET_ERROR_UNSUPPORTED_SPLIT_MODE=The device does not support the segmentation model
NET_ERROR_OPEN_WND_PARAM=Open the window parameter is illegal
NET_ERROR_LIMITED_WND_COUNT=Open the window more than limit
NET_ERROR_UNMATCHED_REQUEST=Request command with the current pattern don't match
NET_RENDER_ENABLELARGEPICADJUSTMENT_ERROR=Render Library to enable high-definition image internal adjustment strategy error
NET_ERROR_UPGRADE_FAILED=Upgrade equipment failure
NET_ERROR_NO_TARGET_DEVICE=Can't find the target device
NET_ERROR_NO_VERIFY_DEVICE=Can't find the verify device 
NET_ERROR_CASCADE_RIGHTLESS=No cascade permissions
NET_ERROR_LOW_PRIORITY=low priority
NET_ERROR_REMOTE_REQUEST_TIMEOUT=The remote device request timeout
NET_ERROR_LIMITED_INPUT_SOURCE=Input source beyond maximum route restrictions
NET_ERROR_SET_LOG_PRINT_INFO=Failed to set log print
NET_ERROR_PARAM_DWSIZE_ERROR="dwSize" is not initialized in input param
NET_ERROR_LIMITED_MONITORWALL_COUNT=TV wall exceed limit
NET_ERROR_PART_PROCESS_FAILED=Fail to execute part of the process
NET_ERROR_TARGET_NOT_SUPPORT=Fail to transmit due to not supported by target
NET_ERROR_VISITE_FILE=Access to the file failed
NET_ERROR_DEVICE_STATUS_BUSY=Device busy
NET_USER_PWD_NOT_AUTHORIZED=Fail to change the password
NET_USER_PWD_NOT_STRONG=Password strength is not enough
NET_ERROR_NO_SUCH_CONFIG=No corresponding setup
NET_ERROR_AUDIO_RECORD_FAILED=Failed to record audio
NET_ERROR_SEND_DATA_FAILED=Failed to send out data 
NET_ERROR_OBSOLESCENT_INTERFACE=Abandoned port 
NET_ERROR_INSUFFICIENT_INTERAL_BUF=Internal buffer is not sufficient 
NET_ERROR_NEED_ENCRYPTION_PASSWORD=verify password when changing device IP
NET_ERROR_NOSUPPORT_RECORD =device not support the record
NET_ERROR_SERIALIZE_ERROR=Failed to serialize data
NET_ERROR_DESERIALIZE_ERROR=Failed to deserialize data
NET_ERROR_LOWRATEWPAN_ID_EXISTED=the wireless id is already existed
NET_ERROR_LOWRATEWPAN_ID_LIMIT=the wireless id limited
NET_ERROR_LOWRATEWPAN_ID_ABNORMAL=add the wireless id abnormaly
NET_ERROR_ENCRYPT=encrypt data fail
NET_ERROR_PWD_ILLEGAL=new password illegal
NET_ERROR_DEVICE_ALREADY_INIT=device is already initiation
NET_ERROR_SECURITY_CODE=security code check out fail
NET_ERROR_SECURITY_CODE_TIMEOUT=security code out of time
NET_ERROR_GET_PWD_SPECI=get password specification fail
NET_ERROR_NO_AUTHORITY_OF_OPERATION=no authority of operation 
NET_ERROR_DECRYPT=decrypt data fail
NET_ERROR_2D_CODE=2D code check out fail
NET_ERROR_INVALID_REQUEST=Invalid request
NET_ERROR_PWD_RESET_DISABLE=password reset disabled
NET_ERROR_PLAY_PRIVATE_DATA=Failed to display private data,such as rule box
NET_ERROR_ROBOT_OPERATE_FAILED=robot operate failed
NET_ERROR_PHOTOSIZE_EXCEEDSLIMIT=photo size exceeds limit
NET_ERROR_USERID_INVALID=Invalid userId 
NET_ERROR_EXTRACTFEATURE_FAILED=photo extract feature failed
NET_ERROR_PHOTO_EXIST=photo exist
NET_ERROR_PHOTO_OVERFLOW=photo over flow
NET_ERROR_CHANNEL_ALREADY_OPENED=channel has already been opened
NET_ERROR_CREATE_SOCKET=create socket error
NET_ERROR_CHANNEL_NUM=Invalid channel number
NET_ERROR_FACE_RECOGNITION_SERVER_GROUP_ID_EXCEED=face recognition server group id exceed

# RealPlay Info
START_REALPLAY=Start RealPlay
STOP_REALPLAY=Stop RealPlay
ATTACH=Attach
DETACH=Detach
REALPLAY_SUCCEED=Success to start realPlay
REALPLAY_FAILED=Failed to start realPlay

CHN=Chn
CHANNEL=Channel
STREAM_TYPE=Stream
MASTER_AND_SUB_STREAM=Master/Sub stream
MASTER_STREAM=Master Stream
SUB_STREAM=Sub Stream

# Capture Picture
LOCAL_CAPTURE=Local Capture
REMOTE_CAPTURE=Remote Capture
TIMER_CAPTURE=Timer Capture
STOP_CAPTURE=Stop Capture
INTERVAL=Interval

TIME_INTERVAL_ILLEGAL=Time Interval Illegal 
PLEASE_START_REALPLAY=Please Start Real Play

# PTZ Info 
PTZ_CONTROL=PTZControl
LEFT_UP=LeftUp
UP=Up
RIGHT_UP=RightUp
LEFT=Left
RIGHT=Right
LEFT_DOWN=LeftDown
DOWN=Down
RIGHT_DOWN=RightDown

ZOOM_ADD=Zoom+
ZOOM_DEC=Zoom-
FOCUS_ADD=Focus+
FOCUS_DEC=Focus-
IRIS_ADD=Iris+
IRIS_DEC=Iris-

SPEED=Speed

#ITS
EVENT_INFO=Event Information
EVENT_NAME=Event Name
EVENT_TIME=Event Time
EVENT_PICTURE=Event and Picture
PLATE_PICTURE=Plate Picture
LICENSE_PLATE=License Plate
PLATE_TYPE=Plate Type
PLATE_COLOR=Plate Color
VEHICLE_TYPE=Vehicle Type
VEHICLE_SIZE=Vehicle Size
VEHICLE_COLOR=Vehicle Color
FILE_COUNT=File Count
FILE_INDEX=File Index
GROUP_ID=Group ID
ILLEGAL_PLACE=Illegal Place
LANE_NUMBER=Lane Number
MANUAL_CAPTURE=Manual Capture
OPEN_STROBE=Open Strobe
CLOSE_STROBE=Close Strobe
INDEX=Index

OPERATE=Operate
FUNCTION=Function

UNDEFINED_COLOR=Undefined Color
BLACK=Black
WHITE=White
RED=Red
BLUE=Blue
GREEN=Green
YELLOW=Yellow
GRAY=Gray
ORANGE=Orange

LIGHT_DUTY=Light Duty
MEDIUM=Medium
OVER_SIZE=Over Size
MINI_SIZE=Miniature Size
LARGE_SIZE=Large Size

NO_PLATENUMBER=No PlateNumber
MANUALSNAP_SUCCEED=Manual snap succeed
MANUALSNAP_FAILED=Manual snap failed

OPEN_STROBE_SUCCEED=Open strobe succeed
OPEN_STROBE_FAILED=Open strobe failed
CLOSE_STROBE_SUCCEED=Close strobe succeed
CLOSE_STROBE_FAILED=Close strobe failed

EVENT_IVS_TRAFFICJUNCTION=Junction
EVENT_IVS_TRAFFIC_RUNREDLIGHT=RunRedLight
EVENT_IVS_TRAFFIC_OVERLINE=OverLine
EVENT_IVS_TRAFFIC_RETROGRADE=ReTrograde
EVENT_IVS_TRAFFIC_TURNLEFT=TurnLeft
EVENT_IVS_TRAFFIC_TURNRIGHT=TurnRight
EVENT_IVS_TRAFFIC_UTURN=UTurn
EVENT_IVS_TRAFFIC_OVERSPEED=OverSpeed
EVENT_IVS_TRAFFIC_UNDERSPEED=UnderSpeed
EVENT_IVS_TRAFFIC_PARKING=Parking
EVENT_IVS_TRAFFIC_WRONGROUTE=WrongRoute
EVENT_IVS_TRAFFIC_CROSSLANE=CrossLane
EVENT_IVS_TRAFFIC_OVERYELLOWLINE=OverYellowLine
EVENT_IVS_TRAFFIC_YELLOWPLATEINLANE=YellowPlateInLane
EVENT_IVS_TRAFFIC_PEDESTRAINPRIORITY=PedestrainPriority
EVENT_IVS_TRAFFIC_MANUALSNAP=ManualSnap
EVENT_IVS_TRAFFIC_VEHICLEINROUTE=VehicleInRoute
EVENT_IVS_TRAFFIC_VEHICLEINBUSROUTE=VehicleInBusRoute
EVENT_IVS_TRAFFIC_BACKING=Backing
EVENT_IVS_TRAFFIC_PARKINGSPACEPARKING=ParkingSpaceParking
EVENT_IVS_TRAFFIC_PARKINGSPACENOPARKING=ParkingSpaceNoParking
EVENT_IVS_TRAFFIC_WITHOUT_SAFEBELT=WithoutSafeBelt

# DownLoad Info
DOWNLOAD_RECORD_BYTIME=DownloadRecordByTime
DOWNLOAD_RECORD_BYFILE=DownloadRecordByFile
QUERY=Query 
DOWNLOAD=DownLoad
STOP_DOWNLOAD=StopDownLoad
START_TIME=Start Time
END_TIME=End Time
RECORD_TYPE=Record Type

GENERAL_RECORD=General Record
ALARM_RECORD=Alarm Record
MOTION_DETECTION=Motion Detection Record
CARD_NUMBER_RECORD=Card Number Record
INTELLIGENT_DETECTION=intelligent Record
POS_RECORD=pos Record


QUERY_RECORD_IS_NOT_EXIST=Query record is not exist
PLEASE_CHECK_RECORD_TIME=Please check record time
PLEASE_SELECT_TIME_AGAIN=Please select time again,max time different is 6 hour
DOWNLOAD_COMPLETED=Download completed

PLEASE_FIRST_QUERY_RECORD=Please first query record
PLEASE_FIRST_SELECT_ROW_WITH_DATA=Please first select row with data

# Time Set
YEAR=Year
MONTH=Month
DAY=Day
HOUR=Hour
MINUTE=Minute
SECOND=Second
CONFIRM=Confirm
CANCEL=Cancel
DATE_CHOOSER=Date Chooser

MONDAY=Mon
TUESDAY=Tue
WEDNESDAY=Wed
THURSDAY=Thur
FRIDAY=Fri
SATURDAY=Sat
SUNDAY=Sun

# Talk
TRANSMIT_TYPE=Transmit Type
LOCAL_TRANSMIT_TYPE=Local(not transmit)
REMOTE_TRANSMIT_TYPE=Remote(transmit)
TRANSMIT_CHANNEL=Speaker Channel
START_TALK=Start Talk
STOP_TALK=Stop Talk
TALK_FAILED=Talk Failed

# DeviceSearchAndInt
DEVICESEARCH_OPERATE=DeviceSearch Operate
DEVICESEARCH_RESULT=DeviceSearch Result
DEVICEINIT=DeviceInit

DEVICESEARCH=DeviceSearch
DEVICE_POINT_TO_POINT_SEARCH=Device IP Point To Point Search

START_SEARCH=Start Search
STOP_SEARCH=Stop Search

START_IP=Start IP
END_IP=End IP

DEVICE_TYPE=Device Type
MAC=MAC
SN=SN
DEVICE_INIT_STATE=Initialized State
INIT_PASSWD=Initialization Password
PHONE=Phone
MAIL=Mail
IP_VERSION=IP Version
SUB_MASK=Sub Mask
GETWAY=GetWay
DETAIL_TYPE=Detail Type
HTTP_PORT=HTTP Port
LOCAL_IP=local ip
CONFIRM_PASSWORD=Confirm Password

OLD_DEVICE=Old Device
DONOT_SUPPORT_INITIALIZATION=Do not support initialization
NOT_INITIALIZED=Uninitialized
INITIALIZED=Initialized

THE_IP_CONTROL_SCOPE=The scope is too large, please control it between the 1000
PLEASE_FIRST_SELECT_INITIALIZED_DEVICE=Please first select initialized device
PLEASE_INPUT_PHONE=Please input phone
PLEASE_INPUT_MAIL=Please input mail
INCONSISTENT=The password and the confirm password are inconsistent
PLEASE_CHECK_IP=Please check IP

SEARCHING_WAITING=Searching, please waiting

START_LISTEN=Start Listen
STOP_LISTEN=Stop Listen
SHOW_ALARM_EVENT=Show Alarm Event Info
ALARM_LISTEN_FAILED=Alarm Listen Failed
ALARM_MESSAGE=Alarm Message

EXTERNAL_ALARM=External alarm 
MOTION_ALARM=Motion detection alarm 
VIDEOLOST_ALARM=Video loss alarm 
SHELTER_ALARM=Camera masking alarm 
DISKFULL_ALARM=HDD full alarm 
DISKERROR_ALARM=HDD error alarm 
START=start
STOP=stop

CURRENT_TIME=Current Time
DEVICE_REBOOT=Device Reboot
SYNCHRONIZE_TIME=Synchronize Time
REBOOT=Reboot
SET_TIME=Set Time
GET_TIME=Get Time
REBOOT_TIPS=Are you sure you want to reboot it?
OPERATE_SUCCESS=Operate Success

#FaceRecognition
FACE_GROUP_ID=Group Id
FACE_GROUP_NAME=Group Name
PERSON_COUNT=Person Count

GROUP_OPERATE=Group Operate
PERSON_OPERATE=Person Operate

FACE_RECOGNITION_EVENT=FaceRecognition Event
FACE_DETECT_EVENT=FaceDetect Event

GLOBAL_PICTURE=Global Picture
PERSON_PICTURE=Person Picture
CANDIDATE_PICTURE=Candidate Picture

TIME=Time
SEX=Sex
AGE=Age
COLOR=Color
EYE=Eye
MOUTH=Mouth
MASK=Mask
BEARD=Beard
MALE=Male
FEMALE=Female
ID_CARD=Id Card
OFFICE_CARD=office card
PASSPORT=Passport
UNIDENTIFIED=Unidentified
HAVE_BEARD=Have Beard
NO_BEARD=No Beard
OPEN_MOUTH=Open Mouth
CLOSE_MOUTH=Close Mouth
YELLOW_COLOR=Yellow
BLACK_COLOR=Black
WHITE_COLOR=White
OPEN_EYE=Open Eye
CLOSE_EYE=Close Eye
SMILE=Smile
ANGER=Anger
SADNESS=Sadness
DISGUST=Disgust
FEAR=Fear
SURPRISE=Surprise
NEUTRAL=Neutral
LAUGH=Laugh
WEAR_MASK=Wear Mask
NO_MASK=No Mask
WEAR_GLASSES=Wear Glasses
NO_GLASSES=No Glasses
UNKNOW=UnKnow
UNLIMITED=Unlimited

NAME=Name
BIRTHDAY=Birthday
ID_NO=Id No
ID_TYPE=ID Type
SIMILARITY=Similarity
UID=UID
STRANGER=Stranger
GLASSES=glasses
PICTURE_PATH=picture path
FACE_LIBRARY_ID=face library id
FACE_LIBRARY_NAME=face library name


ADD=Add
MODIFY=Modify
FRESH=Fresh
ADD_GROUP=Add Group
MODIFY_GROUP=Modify Group
DEL_GROUP=Delete Group
DISPOSITION=Disposition
DEL_DISPOSITION=Delete Disposition

FIND_CONDITION=Find Condition
FIND_PERSON=Find Person
ADD_PERSON=Add Person
MODIFY_PERSON=Modify Person
DEL_PERSON=Delete Person

PREVIOUSPAGE=Previous Page
LASTPAGE=Last Page
SELECT_PICTURE=Select Picture
SEARCH_BY_PIC=Search by Picture
DOWNLOAD_QUERY_PICTURE=Download the query Picture
FACE_LIBRARY=face library
HISTORY_LIBRARY=history library
CHOOSE_FACE_PIC=Please select a face picture
EVENT_TYPE=Event Type
PAGES_NUMBER=Pages Number

SIMILARITY_RANGE=Similarity range[0, 100]
PLEASE_INPUT_GROUPNAME=Please input groupName
PLEASE_SELECT_GROUP=Please select group

PLEASE_SELECT_PERSON=Please select person
PLEASE_ADD_DISPOSITION_INFO=Please add disposition info
PLEASE_SELECT_DEL_DISPOSITION_INFO=Please select delDisposition info

#AutoRegister
AUTOREGISTER_LISTEN=AutoRegister Listen
DEVICE_CONFIG=Device Configure
DEVICE_LIST=Device List
DEVICE_MANAGER=Device Manager
ADD_DEVICE=Add Device
MODIFY_DEVICE=Modify Device
DELETE_DEVICE=Delete Device
CLEAR_DEVICE=Clear Device
IMPORT_DEVICE=Import Device
EXPORT_DEVICE=Export Device
DEVICE_ID=Device ID
ENABLE=Enable
REGISTER_ADDRESS=Register Address
REGISTER_PORT=Register Port
GET=Get
SET=Set
RECORD=Record
DEVICE_LOGIN=Device has logged in

ALREADY_EXISTED=Already existed
ALREADY_EXISTED_WHETHER_OR_NOT_TO_COVER=Already existed, whether or not to cover(no, not export)
FILE_OPEN_PLEASE_CLOSE_FILE=The file is opened, please first close the file
IMPORT_COMPLETION=Import completed
EXPORT_COMPLETION=Export completed
FILE_NOT_EXIST=File is not existed

PLEASE_INPUT=Please input 
MAX_SUPPORT_100=Maximum support is 100

#Attendance
ATTENDANCE=Attendance
USER_ID=User ID
USER_NAME=User Name
CARD_NO=Card No
QUERY_CONDITION=Query Condition
USER_OPERATE=User Operate
FINGERPRINT_OPERATE=FingerPrint Operate
OPERATE_BY_USER_ID=Operate FingerPrint By User ID
OPERATE_BY_FINGERPRINT_ID=Operate FingerPrint By FingerPrint ID
FINGERPRINT_ID=FingerPrint ID
DELETE=Delete
SEARCH=Search
SEARCH_FINGERPRINT=Search FingerPrint
ADD_FINGERPRINT=Add FingerPrint
DELETE_FINGERPRINT=Delete FingerPrint
SUBSCRIBE=Subscribe
UNSUBSCRIBE=UnSubscribe
USER_LIST=User List
NEXT_PAGE=Next Page
USER_INFO=User Info
DOOROPEN_METHOD=Open Door Method
FINGERPRINT=FingerPrint
FINGERPRINT_INFO=FingerPrint Info
FINGERPRINT_DATA=FingerPrint Data
CARD=Card
DELETE_FINGERPRINT_PROMPT=It will delete all fingerprint of this user
SUBSCRIBE_FAILED=Subscribe failed
FINGERPRINT_ID_ILLEGAL=FingerPrint id illegal
FINGERPRINT_COLLECTION=FingerPrint Collection
START_COLLECTION=Start Collection
STOP_COLLECTION=Stop Collection
IN_THE_COLLECTION=In collection...
COLLECTION_COMPLETED=Collection completed
COLLECTION_FAILED=Collection failed
FINGERPRINT_ID_NOT_EXIST=FingerPrint Id not exist
USER_ID_EXCEED_LENGTH=User id exceed max length
USER_NAME_EXCEED_LENGTH=User name exceed max length
CARD_NO_EXCEED_LENGTH=Card no exceed max length
CARD_NAME_EXCEED_LENGTH=Card name exceed max length
CARD_PASSWD_EXCEED_LENGTH=Card password exceed max length

#Gate
GATE=FaceOpenDoor

CARD_OPERATE=Card Operate
CARD_INFO=Card Information
CARD_MANAGER=Card Manager

CLEAR=Clear

OPEN_STATUS=Open Status
OPEN_METHOD=Open Method

TEMPERATURE=temperture
MASK_STATUS=mask status

CARD_UNKNOW=UnKnown Card
CARD_GENERAL=General Card
CARD_VIP=VIP Card
CARD_GUEST=Guest Card
CARD_PATROL=Patrol Card
CARD_BACKLIST=BackList Card
CARD_COERCE=Coerce Card
CARD_POLLING=Polling Card
CARD_MOTHERCARD=Mother Card

STATE_UNKNOWN=UnKnown
STATE_NORMAL=Normal
STATE_LOSE=Lose
STATE_LOGOFF=LogOff
STATE_FREEZE=Freeze
STATE_ARREARS=Arrears
STATE_OVERDUE=OverDue
STATE_PREARREARS=PreArrears

RECORD_NO=Record No
CARD_NAME=Card Name
CARD_STATUS=Card Status
CARD_PASSWORD=Card Password
CARD_TYPE=Card Type
CARD_NUM=Card Number
USE_TIMES=Use Times
IS_FIRST_ENTER=IsFirstEnter
IS_VALID=IsValid
VALID_PERIOD=Valid Period
VALID_START_TIME=Valid Start Time
VALID_END_TIME=Valid End Time
FIRST_ENTER=FirstEnter
NO_FIRST_ENTER=No FirstEnter
VALID=Valid
INVALID=Invalid

PLEASE_SELECT_CARD=Please select card
PLEASE_INPUT_CARDNO=Please input cardNo
PLEASE_INPUT_USERID=Please input userId
WANT_CLEAR_ALL_INFO=Do you want to clear all information ?

ADD_CARD_INDO_FAILED=Failed to add card information 
ADD_CARD_INFO_AND_PERSON_PICTURE_SUCCEED=Succeed to add card information and person picture
ADD_CARD_INFO_SUCCEED_BUT_ADD_PERSON_PICTURE_FAILED=Succeed to add card information, but failed to add person picture
CARD_EXISTED_ADD_PERSON_PICTURE_SUCCEED=Card information is existed, succeed to add person picture

MODIFY_CARD_INFO_SUCCEED=Succeed to modify card information
MODIFY_CARD_INFO_FAILED=Failed to modify card information
MODIFY_CARD_INFO_AND_PERSON_PICTURE_SUCCEED=Succeed to modify card information and person picture
MODIFY_CARD_INFO_SUCCEED_BUT_MODIFY_PERSON_PICTURE_FAILED=Succeed to modify card information, but failed to modify person picture

NET_ACCESS_DOOROPEN_METHOD_UNKNOWN=UnKnow
NET_ACCESS_DOOROPEN_METHOD_PWD_ONLY=Password
NET_ACCESS_DOOROPEN_METHOD_CARD=Card
NET_ACCESS_DOOROPEN_METHOD_CARD_FIRST=First Card Then Password
NET_ACCESS_DOOROPEN_METHOD_PWD_FIRST=First Password Then Card 
NET_ACCESS_DOOROPEN_METHOD_REMOTE=Remote Open
NET_ACCESS_DOOROPEN_METHOD_BUTTON=Open Door Button
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT=fingerprint lock
NET_ACCESS_DOOROPEN_METHOD_PWD_CARD_FINGERPRINT=password+swipe card+fingerprint combination unlock
NET_ACCESS_DOOROPEN_METHOD_PWD_FINGERPRINT=password+fingerprint combination unlock
NET_ACCESS_DOOROPEN_METHOD_CARD_FINGERPRINT=swipe card+fingerprint combination unlock
NET_ACCESS_DOOROPEN_METHOD_PERSONS=multi-people unlock
NET_ACCESS_DOOROPEN_METHOD_KEY=Key
NET_ACCESS_DOOROPEN_METHOD_COERCE_PWD=Use force password to open the door   
NET_ACCESS_DOOROPEN_METHOD_QRCODE=Use QR Code  
NET_ACCESS_DOOROPEN_METHOD_FACE_RECOGNITION=face recogniton to open the door
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD=comparsion of face and ID card
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD_AND_IDCARD=ID card  and compasion of face and ID card
NET_ACCESS_DOOROPEN_METHOD_BLUETOOTH=Bluetooth
NET_ACCESS_DOOROPEN_METHOD_CUSTOM_PASSWORD=Custom password
NET_ACCESS_DOOROPEN_METHOD_USERID_AND_PWD=UserID and password
NET_ACCESS_DOOROPEN_METHOD_FACE_AND_PWD=Face and password
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_PWD=Fingerprint and password
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_FACE= FingerPrint and face
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FACE=Card and face
NET_ACCESS_DOOROPEN_METHOD_FACE_OR_PWD=Face or password
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_PWD=Fingerprint or password
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_FACE=Fingerprint or face
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FACE=Card or face
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT=Card or fingerprint
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_AND_FACE_AND_PWD=Fingerprint and face and password
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FACE_AND_PWD=Card and face and password
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FINGERPRINT_AND_PWD=Card and fingerprint and password
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_PWD_AND_FACE=Card and password and face
NET_ACCESS_DOOROPEN_METHOD_FINGERPRINT_OR_FACE_OR_PWD=Fingerprint or face or password
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FACE_OR_PWD=Card or face or password
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT_OR_FACE=Card or fingerprint or face
NET_ACCESS_DOOROPEN_METHOD_CARD_AND_FINGERPRINT_AND_FACE_AND_PWD=Card and fingerprint and face and password 
NET_ACCESS_DOOROPEN_METHOD_CARD_OR_FINGERPRINT_OR_FACE_OR_PWD=Card or fingerprint or face or password
NET_ACCESS_DOOROPEN_METHOD_FACEIPCARDANDIDCARD_OR_CARD_OR_FACE=ID card  and compasion of face and ID card or card or face
NET_ACCESS_DOOROPEN_METHOD_FACEIDCARD_OR_CARD_OR_FACE=ID card  and compasion of face or card or face

#ThermalCamera
THERMAL_CAMERA=Thermal Camera
THERMAL_OPERATE=Operate
POINT_QUERY=Query Point
ITEM_QUERY=Query Item
TEMPER_QUERY=Query Temperature 
HEATMAP=HeatMap
POINT_TEMPER=Point Temperature
ITEM_TEMPER=Item Temperature
X=X
Y=Y
COORDINATE_ILLEGAL=Coordinate Illegal
QUERY_RESULT=Query Result
METER_TYPE=Meter Type
TEMPER_UNIT=Temperature Unit
TEMPER=Temperature
UNKNOWN=Unknown
SPOT=Spot
LINE=Line
AREA=Area
CENTIGRADE=Celsius
FAHRENHEIT=Fahrenheit
PRESET_ID=Preset Id
RULE_ID=Rule Id
TEMPER_AVER=Average Temperature
TEMPER_MAX=Maximum Temperature
TEMPER_MIN=Minimum Temperature
TEMPER_MID=Middle Temperature
TEMPER_STD=Standard Deviation
INPUT_ILLEGAL=Input Illegal
TEMPER_INFO=Temperature Info
FIVE_MINUTES=Five Minutes
TEN_MINUTES=Ten Minutes
FIFTEEN_MINUTES=Fifteen Minutes
THIRTY_MINUTES=Thirty Minutes
SAVE_PERIOD=Save Period
QUERY_LIST=Query List
RECORD_TIME=Record Time
ITEM_NAME=Name
COORDINATE=Coordinate
NO_RECORD=No Record
HEATMAP_OPERATE=Operate
IDLE=Idle
ACQUIRING=Acquiring
RADIOMETRY_ATTACH=Attach
RADIOMETRY_DETACH=Detach
RADIOMETRY_FETCH=Fetch
SAVE_HEATMAP=Save HeatMap
HEATMAP_METADATA_INFO=HeatMap MetaData
HEIGHT=Height
WIDTH=Width
LENGTH=Size
SENSOR_TYPE=Sensor Type
HEATMAP_SAVE_SUCCESS=HeatMap Save Success

#matrix screen
MATRIX_SCREEN=Lattice Screen
PASSING_STATE=Passing state
PASSING_CAR=pass car
NO_CAR=no car
IN_TIME=Entry time
OUT_TIME=departure time
PLATE_NUMBER=plate number
CAR_OWNER=car owner
PARKING_TIME=parking time
USER_TYPE=user type
MONTHLY_CARD_USER=monthly card user
ANNUAL_CARD_USER=annual card user
LONG_TERM_USER=long term user/VIP
TEMPORARY_USER=temporary user/Visitor
PARKING_CHARGE=parking charge
DAYS_DUE=days due
REMAINING_PARKING_SPACES=Remaining parking spaces
VEHICLES_NOT_ALLOWED_TO_PASS=vehicles not allowed to pass
ALLOWED_VEHICLES_TO_PASS=allowed vehicles to pass
SET_UP=set up
SUCCESSFULLY_ISSUED=Successfully issued
DELIVERY FAILED=Delivery failed
CUSTOM_USER_CLASS=Custom user class
REMARKS_INFORMATION=Remarks information
CUSTOM_INFORMATION=Custom information

# human number statistic
HUMAN_NUMBER_STATISTIC_TITLE=Human Number Statistic
HUMAN_NUMBER_STATISTIC_CONTROL=Human Number Statistic Control

HUMAN_NUMBER_STATISTIC_EVENT_TITLE=Human Number Statistic Event List

HUMAN_NUMBER_STATISTIC_EVENT_CHANNEL=Channel
HUMAN_NUMBER_STATISTIC_EVENT_TIME=EventTime
HUMAN_NUMBER_STATISTIC_EVENT_HOUR_IN=HourIn
HUMAN_NUMBER_STATISTIC_EVENT_HOUR_OUT=HourOut
HUMAN_NUMBER_STATISTIC_EVENT_TODAY_IN=TodayIn
HUMAN_NUMBER_STATISTIC_EVENT_TODAY_OUT=TodayOut
HUMAN_NUMBER_STATISTIC_EVENT_TOTAL_IN=TotalIn
HUMAN_NUMBER_STATISTIC_EVENT_TOTAL_OUT=TotalOut

HUMAN_NUMBER_STATIC_EVENT_OSD_CLEAR=OSD Clear

VTO_ALARM_EVENT_ROOM_NO=RoomNo.
VTO_ALARM_EVENT_CARD_NO=CardNo.
VTO_ALARM_EVENT_TIME=Time
VTO_ALARM_EVENT_OPEN_METHOD=OpenMethod
VTO_ALARM_EVENT_STATUS=Status

VTO_REAL_LOAD_ROOM_NO=RoomNo.
VTO_REAL_LOAD_CARD_NO=CardNO.
VTO_REAL_LOAD_TIME=Time
VTO_REAL_LOAD_EVENT_INFO=EventInfo

VTO_OPERATE_MANAGER_TITLE=Card&Fingerprint&FaceManager
VTO_OPERATE_MANAGER_REC_NO=RecNo.
VTO_OPERATE_MANAGER_ROOM_NO=RoomNo.
VTO_OPERATE_MANAGER_CARD_NO=CardNo.
VTO_OPERATE_MANAGER_FINGER_PRINT_DATA=FingerprintData
VTO_OPERATE_INFO_TITLE=Add

VTO_OPERATE_COLLECTION_FINGER_PRINT_TITLE=CollectionFingerPrint

DOOR_OPEN=Open Door
DOOR_CLOSE=Close Door
EVENT_OPERATE=Event Operate
START_REAL_LOAD_PIC=StartRealLoad
STOP_REAL_LOAD_PIC=StopRealLoad
ALARM_EVENT=AlarmEvent
REAL_LOAD_EVENT=RealLoadEvent
COLLECTION_RESULT=CollectionResult
NEED_FINGER_PRINT=Need FingerPrint
FACE_INFO=FaceInfo
OPEN=open
VTO=VTO
SCADA=SCADA
MODIFY_CARD_FACE_FAILED=failed to modify face info.
EM_MASK_STATE_UNKNOWN=unknown
EM_MASK_STATE_NODISTI=unrecognized
EM_MASK_STATE_NOMASK=Not wearing mask
EM_MASK_STATE_WEAR=wearing mask
END_SEARCH=End of query
DOWNLOAD_PICTURE=download picture
ENTER_PICTURE_PATH=Please enter the picture path
LOADING=The equipment is being processed
QUERY_CARD_EXIST_FAILED=Check if the cardNo exists failed
CARD_EXIST=The cardNo already exists
INPUT_ROOM_NO=please input room number
ROOM_NO_EXCEED_LENGTH=room number exceed max length
REMOVE_CARD_FACE_FAILED=remove card face failed
STOP_LISTEN_FAILED=stop listen failed

SCREEN_NUMBER=screen number
TEXT_CONTENT=text content
TEXT_CONTENT_TYPE=text content type
TEXT_CONTENT_COLOR=text content color
SCROLL_TYPE=scroll type
SCROLL_SPEED=scroll speed

ORDINARY=ordinary
QR_CODE=QR code
LOCAL_TIME=local time
RESOURCE=resource

NOT_ROLLING=not rolling 
SCROLL_LEFT_AND_RIGHT=scroll left and right
SCROLL_UP_AND_DOWN=scroll up and right

NUMBER_STRING=number string
VOICE_TEXT=vioce text
ISSUED=issued
PROMPT=Please add corresponding parameters


SCADA_DEVICE_LIST=device list

DEVICE_NAME=device name
POINT_ID=point id
IF_VALID_SIGNAL_POINT=valid signal point
POINT_NAME=point name
ALARM_DESCRIBE=alarm describe
ALARM_LEVEL=alarm level
ALARM_DELAY=alarm delay
ALARM_TYPE=alarm type
ALARM_TIME=alarm time
COLLECT_TIME=collect time

SCADA_POINT_LIST=point list
SCADA_ATTACH_ALARM=attach alarm
SCADA_ATTACH_INFO=attach info
SCADA_ATTACH=start listen
BTN_LIST = get
