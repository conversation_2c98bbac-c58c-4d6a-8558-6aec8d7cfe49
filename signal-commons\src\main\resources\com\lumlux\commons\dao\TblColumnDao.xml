<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lumlux.commons.dao.TblColumnDao">

	<insert id="addDisplay">
		INSERT INTO tbl_display(
			id,column_id,plan_type,start_time,
			end_time,plan,create_by,create_date,update_by,update_date,remarks,del_flag
		) VALUES (
					 #{id},
					 #{columnId},
					 #{planType},
					 #{startTime},
					 #{endTime},
					 #{plan},
					 #{createBy},
					 #{createDate},
					 #{updateBy},
					 #{updateDate},
					 #{remarks},
					 #{delFlag}
				 )
	</insert>

	<update id="updateColumnConfig">
		update tbl_alarm_config
		set
			state_interval=#{stateInterval},
			status_offline_interval=#{statusOfflineInterval},
			status_part_invalid_interval=#{statusPartInvalidInterval},
			update_by=#{updateBy},
			update_date=#{updateDate}
		WHERE
			collector_id = #{id}
	</update>

	<update id="updateDisplay">
		update tbl_display
		set
					 start_time=#{startTime},
					 end_time=#{endTime},
					 plan=#{plan},
					 update_by=#{updateBy},
					 update_date=#{updateDate},
					 remarks=#{remarks}
			WHERE
			id = #{id}
	</update>
	<insert id="addHorn">
		INSERT INTO tbl_horn(
			id,column_id,plan_type,start_time,
			end_time,plan,create_by,create_date,update_by,update_date,remarks,del_flag
		) VALUES (
					 #{id},
					 #{columnId},
					 #{planType},
					 #{startTime},
					 #{endTime},
					 #{plan},
					 #{createBy},
					 #{createDate},
					 #{updateBy},
					 #{updateDate},
					 #{remarks},
					 #{delFlag}
				 )
	</insert>
	<update id="updateHorn">
		update tbl_horn
			set
			start_time=#{startTime},
			end_time=#{endTime},
			plan=#{plan},
			update_by=#{updateBy},
			update_date=#{updateDate},
			remarks=#{remarks}
			WHERE
			id = #{id}
	</update>
	<insert id="addProjector">
		INSERT INTO tbl_projector(
			id,column_id,plan_type,start_time,
			end_time,plan,create_by,create_date,update_by,update_date,remarks,del_flag,projector_id,`type`
		) VALUES (
					 #{id},
					 #{columnId},
					 #{planType},
					 #{startTime},
					 #{endTime},
					 #{plan},
					 #{createBy},
					 #{createDate},
					 #{updateBy},
					 #{updateDate},
					 #{remarks},
					 #{delFlag},
					 #{projectorId},
					 #{type}



				 )
	</insert>

	<update id="updateProjector">
		update tbl_projector
		set
					 start_time=#{startTime},
					 end_time=#{endTime},
					 plan=#{plan},
					 update_by=#{updateBy},
					 update_date=#{updateDate},
					 remarks=#{remarks},
					 `type`=#{type}

					 WHERE
			  id = #{id}
	</update>


	<insert id="addColumn">
		INSERT INTO tbl_column(
			id,`name`,code,model,area_id,enabled,status,supplier_id,support_id,
			office_id,failure_status,failure_date,prod_delivery_time,prod_end_time,prod_warranty_status,device_install_time,iccid,
			attitude_enabled,attitude_x_shaft,attitude_y_shaft,attitude_z_shaft,column_status,column_power,longitude,
			latitude,qnline_threshold,create_by,create_date,update_by,update_date,address,remarks,del_flag,
			red_projector_id,red_projector_enabled,red_projector_status,red_projector_power,
			green_projector_id,green_projector_enabled,green_projector_status,green_projector_power,
			horn_enabled,horn_status,display_enabled,display_status,attitude_status
		) VALUES (
					 #{id},
					 #{name},
					 #{code},
					 #{model},
					 #{areaId},
					 #{enabled},
					 #{status},
					 #{supplierId},
					 #{supportId},
					 #{officeId},
					 #{failureStatus},
					 #{failureDate},
					 #{prodDeliveryTime},
					 #{prodEndTime},
					 #{prodWarrantyStatus},
					 #{deviceInstallTime},
					 #{iccid},
					 #{attitudeEnabled},
					 #{xShaft},
					 #{yShaft},
					 #{zShaft},
					 #{columnStatus},
					 #{columnPower},
					 #{longitude},
					 #{latitude},
					 #{qnlineThreshold},
					 #{createBy},
					 #{createDate},
					 #{updateBy},
					 #{updateDate},
					 #{address},
					 #{remarks},
					 #{delFlag},
					 #{redProjectorId},
					 #{redProjectorEnabled},
					 #{redProjectorStatus},
					 #{redProjectorPower},
					 #{greenProjectorId},
					 #{greenProjectorEnabled},
					 #{greenProjectorStatus},
					 #{greenProjectorPower},
					 #{hornEnabled},
					 #{hornStatus},
					 #{displayEnabled},
					 #{displayStatus},
					 #{attitudeStatus}
						 )
	</insert>

	<update id="updateColumn">
		UPDATE tbl_column
		SET
			`model`=#{model},
			`name`=#{name},
			code=#{code},
			area_id=#{areaId},
			enabled=#{enabled},
			supplier_id=#{supplierId},
			support_id=#{supportId},
			office_id=#{officeId},
			prod_delivery_time=#{prodDeliveryTime},
			prod_end_time=#{prodEndTime},
			device_install_time=#{deviceInstallTime},
			iccid=#{iccid},
			attitude_enabled=#{attitudeEnabled},
			longitude=#{longitude},
			latitude=#{latitude},
			qnline_threshold=#{qnlineThreshold},
			update_by=#{updateBy},
			update_date=#{updateDate},
			address= #{address},
			remarks=#{remarks},
			red_projector_enabled=#{redProjectorEnabled},
			green_projector_enabled=#{greenProjectorEnabled},
			horn_enabled=#{hornEnabled},
			display_enabled=#{displayEnabled}
		WHERE
			id = #{id}
	</update>

	<select id="selectVariablesByArea" resultType="com.lumlux.commons.entity.TblColumn" >
		SELECT a.*,	t1.name AS "supplierName",
		t2.name AS "supportName", a4.name AS "areaName",
		o15.name AS "officeName",
		b.label AS "columnFailureStatusDesc",
		cc.label AS "communicationStatusDesc",
		ccc.label AS "attitudeStatusDesc"
		FROM  tbl_column a
		LEFT JOIN sys_area a4 ON a4.id = a.area_id
		LEFT JOIN sys_office o15 ON o15.id = a.office_id
		LEFT JOIN tbl_supplier t1 ON t1.id = a.supplier_id
		LEFT JOIN tbl_support t2 ON t2.id = a.support_id
		LEFT JOIN sys_dict b ON  a.failure_status = b.value AND b.type = 'column_failure_status'
		LEFT JOIN sys_dict c ON c.value = a.model AND c.type = 'column_type'
		LEFT JOIN sys_dict cc ON cc.value = a.status AND cc.type = 'status'
		LEFT JOIN sys_dict ccc ON ccc.value = a.attitude_status AND ccc.type = 'attitude_status'
		<where>
			    a.del_flag='0'
			<if test="model != null and model != ''">
				and a.model= #{model}
			</if>
			<if test="office != null and office != ''">
				AND o15.id =#{office}
			</if>
			<if test="supportId != null and supportId != ''">
				AND t2.id =#{supportId}
			</if>
			<if test="model != null and model != ''">
				AND a.model =#{model}
			</if>
			<if test="areaId != null and areaId != ''">
				AND (a4.id=#{areaId} or a4.parent_ids LIKE concat('%',#{areaId},'%'))
			</if>
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="listCode" resultType="java.lang.String">
		select `code` from tbl_column WHERE code=#{code} and del_flag=0
	</select>



	<select id="getFailureHistorys" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT
		c.name as name,
		c.address as address,
		a.description as description,
		a.status status,
        a.end_date endDate,
		a.create_date as create_date,
		a.update_date as update_date
		from tbl_failure a
		left join tbl_column c on a.collector_id = c.id and a.del_flag= c.del_flag
		WHERE a.del_flag = '0'

		<if test="description !=null and description != ''">
			AND a.description LIKE concat('%',#{description},'%')
		</if>
		<if test="collectorId !=null and collectorId != ''">
			AND a.collector_id = #{collectorId}
		</if>
		<if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
			AND a.create_date between #{startDate} and #{endDate}
		</if>
		<if test="model !=null and model != ''">
			AND c.model = #{model}
		</if>
		ORDER BY a.update_date DESC
	</select>


	<select id="listColumnHistory" resultType="com.lumlux.commons.entity.TblColumnHistory">
		SELECT h.*, c.name as name from tbl_column_history h
		LEFT JOIN tbl_column c on h.column_id = c.id and h.del_flag= c.del_flag
		WHERE c.del_flag = '0'
		<if test="collectorId !=null and collectorId != ''">
			AND h.column_id = #{collectorId}
		</if>
		<if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
			AND h.create_date between #{startDate} and #{endDate}
		</if>
		<if test="model !=null and model != '' ">
			AND c.model = #{model}
		</if>
		ORDER BY h.update_date DESC
	</select>



	<select id="get" resultType="com.lumlux.commons.entity.TblColumn">
		SELECT
		a.id AS "id",
		a.name AS "name",
		a.model AS "model",
		a.enabled AS "enabled",
		a.iccid as "iccid",
		a.code AS "code",
		a.status AS "status",
		a.column_status AS "columnStatus",
		a.longitude AS "longitude",
		a.latitude AS "latitude",
		a.address AS "address",
		a.device_install_time as  "deviceInstallTime",
		a.supplier_id AS "supplierId",
		a.office_id AS "officeId",
		a.support_id AS "supportId",
		a.area_id AS "areaId",
		a.prod_delivery_time as "prodDeliveryTime",
		a.prod_end_time as "prodEndTime",
		t1.name AS "supplierName",
		t2.name AS "supportName",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.column_power AS "columnPower",
      	a.display_enabled AS "displayEnabled",
		a.display_status AS "displayStatus",
		d2.label AS "displayStatusDesc",
		a.horn_enabled AS "hornEnabled",
		a.horn_status AS "hornStatus",
		d3.label AS "hornStatusDesc",
		a.attitude_enabled as "attitudeEnabled",
		a.attitude_status as "attitudeStatus",
		d4.label as "attitudeStatusDesc",
		a.green_projector_power AS "greenProjectorPower",
		a.green_projector_enabled AS "greenProjectorEnabled",
		a.green_projector_status AS "greenProjectorStatus",
		d5.label AS "greenProjectorStatusDesc",
		a.red_projector_power AS "redProjectorPower",
		a.red_projector_enabled AS "redProjectorEnabled",
		a.red_projector_status AS "redProjectorStatus",
		d6.label AS "redProjectorStatusDesc",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a4.name AS "area.name",
		o15.name AS "office.name",
		a.failure_date AS "failureDate",
		a.prod_warranty_status as "prodWarrantyStatus",
		a.qnline_threshold as "qnlineThreshold",
		d1.label AS "communicationStatusDesc"
		FROM tbl_column a
				 LEFT JOIN sys_area a4 ON a4.id = a.area_id
				 LEFT JOIN sys_office o15 ON o15.id = a.office_id
				 LEFT JOIN tbl_supplier t1 ON t1.id = a.supplier_id
				 LEFT JOIN tbl_support t2 ON t2.id = a.support_id
				 left join sys_dict d1 on a.status = d1.value and d1.type = 'status'
				 left join sys_dict d2 on a.display_status = d2.value and d2.type = 'display_status'
				 left join sys_dict d3 on a.horn_status = d3.value and d3.type = 'horn_status'
				 left join sys_dict d4 on a.attitude_status = d4.value and d4.type = 'attitude_status'
				 left join sys_dict d5 on a.red_projector_status = d5.value and d5.type = 'red_projector_status'
				 left join sys_dict d6 on a.green_projector_status = d6.value and d6.type = 'green_projector_status'
-- 				 left join sys_dict d7 on a.column_status = d7.value and d7.type = 'column_status'
				 left join sys_dict d8 on a.model = d8.value and d8.type = 'column_type'
		WHERE a.id = #{id}
	</select>

	<update id="enableColumn">
		update tbl_column set enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0
	</update>

	<update id="enableRedProjector">
		update tbl_column set red_projector_enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0
	</update>

	<update id="enableGreenProjector">
		update tbl_column set green_projector_enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0
	</update>

	<update id="enableHorn">
		update tbl_column set horn_enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0
	</update>

	<update id="enableDisplay">
		update tbl_column set display_enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0

	</update>

	<update id="enableAttitude">
		update tbl_column set attitude_enabled = #{0}
		where id = #{1} and  model=#{2} and del_flag= 0
	</update>

	<update id="delete">
		UPDATE tbl_column SET
			del_flag ='1'
		WHERE id = #{id}
	</update>

	<select id="getThresholdXYZ" resultType="com.lumlux.commons.entity.TblColumn">
		select
		*
		from tbl_column a where  a.id = #{columnId} and a.del_flag =0  limit 1
	</select>

	<select id="listFailureHistorys" resultType="java.util.Map">

		SELECT
		c.name as name,
		c.address as address,
		a.description as description,
		b.label status,
		a.end_date endDate,
		a.create_date as create_date,
		a.update_date as update_date
		from tbl_failure a
		left join tbl_column c on a.collector_id = c.id and a.del_flag= c.del_flag
		LEFT JOIN sys_dict b ON  a.`status` = b.`value`  AND b.type = 'failure_status'
		WHERE a.del_flag = '0'

		<if test="supportId !=null and supportId != ''">
			AND c.support_id=#{supportId}
		</if>
		<if test="description !=null and description != ''">
			AND a.description LIKE concat('%',#{description},'%')
		</if>
		<if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
			AND a.create_date between #{startDate} and #{endDate}
		</if>
		<if test="columnName !=null and columnName != ''">
			AND c.name LIKE concat('%',#{columnName},'%')
		</if>
		ORDER BY a.update_date DESC
	</select>

	<select id="getSupplyAnaly" resultType="com.lumlux.commons.entity.ColumnSupplier">
		SELECT tbsu.name name,count(*) number from  tbl_column tbco left join  tbl_supplier tbsu on  tbsu.id=  tbco.supplier_id
		WHERE tbsu.name !='' and tbsu.del_flag='0' and tbco.del_flag='0'
		GROUP BY tbsu.name
    </select>

	<select id="columnSupplyStatus" resultType="java.lang.Integer">
		SELECT count(*) collumnSum from tbl_column
		where del_flag = '0' and model=#{model}
	</select>

	<select id="getColumnSupplyStatusInWar" resultType="java.lang.Integer">
		SELECT   count(*)
		from tbl_column
		where del_flag = '0' and model=#{model} and prod_warranty_status ='1'
	</select>

	<select id="getColumnSupplyStatusOutWar" resultType="java.lang.Integer">
		SELECT   count(*)
		from tbl_column
		where del_flag = '0' and model=#{model} and prod_warranty_status !='1'
	</select>

	<select id="getColumnSupplyStatusFailure" resultType="java.lang.Integer">
		select count(*) from tbl_column  WHERE `failure_status` = 1 and del_flag = '0' and model=#{model}
	</select>

	<select id="getColumnSupplyStatusOnline" resultType="java.lang.Integer">
		select count(*) from tbl_column  WHERE `status` = 0 and del_flag = '0' and model=#{model}
	</select>

	<select id="getControllerSum" resultType="java.lang.Integer">
		SELECT count(*) collumnSum from tbl_column
		where del_flag = '0'
	</select>

	<select id="getColumnSupplyStatusShaft" resultType="java.lang.Integer">
		select count(*) from tbl_column  WHERE `attitude_status` = 1 and del_flag = '0' and model=#{model}
	</select>

	<select id="findCode" resultType="java.lang.String">
		select code from tbl_column where del_flag = '0'
	</select>

	<select id="getCrossroadSum" resultType="java.lang.Integer">
		select count(*) from sys_area  WHERE  del_flag = '0' and type='5'
	</select>

	<select id="getProSum" resultType="java.lang.Integer">
		select count(DISTINCT(`name`)) from sys_area WHERE type=2 and del_flag=0 and `name` is not null
	</select>

	<select id="getColInfo" resultType="java.util.Map">
		select name, status,failure_status,longitude,latitude from tbl_column  WHERE  del_flag='0'
	</select>
</mapper>
