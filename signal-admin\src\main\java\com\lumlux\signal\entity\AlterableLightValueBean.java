package com.lumlux.signal.entity;

/**
 * <p>Title: AlterableLightValueBean.java</p>
 * <p>Description: AlterableLightValueBean单表表单</p>
 * <p>Author: <PERSON></p>
 * <p>Date: 2024/5/24</p>
 * <p>Company: Shanghai Austar Lighting Electrical Industry Co., Ltd.</p>
 */
public class AlterableLightValueBean {

    private String lightNumber;//灯号
    private String lightType;//灯类型
    private String frequentGreen;//绿灯时长
    private String redTime;//红计时
    private String greenTime;//绿计时
    private Integer lightStatus;//灯状态 1黑灯 0正常

    public Integer getLightStatus() {
        return lightStatus;
    }

    public void setLightStatus(Integer lightStatus) {
        this.lightStatus = lightStatus;
    }

    private String greenStartTime; // 绿灯开始时间以s为单位

    public String getLightNumber() {
        return lightNumber;
    }

    public void setLightNumber(String lightNumber) {
        this.lightNumber = lightNumber;
    }

    public String getLightType() {
        return lightType;
    }

    public void setLightType(String lightType) {
        this.lightType = lightType;
    }

    public String getFrequentGreen() {
        return frequentGreen;
    }

    public void setFrequentGreen(String frequentGreen) {
        this.frequentGreen = frequentGreen;
    }

    public String getRedTime() {
        return redTime;
    }

    public void setRedTime(String redTime) {
        this.redTime = redTime;
    }

    public String getGreenTime() {
        return greenTime;
    }

    public void setGreenTime(String greenTime) {
        this.greenTime = greenTime;
    }

    public String getGreenStartTime() {
        return greenStartTime;
    }

    public void setGreenStartTime(String greenStartTime) {
        this.greenStartTime = greenStartTime;
    }
}
