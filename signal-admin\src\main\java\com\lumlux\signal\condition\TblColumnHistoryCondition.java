package com.lumlux.signal.condition;

import com.heinqi.yangtes.jee.base.AbstractCondition;
import com.heinqi.yangtes.jee.modules.sys.entity.Office;
import com.lumlux.commons.entity.TblColumnHistory;

import java.util.List;


public class TblColumnHistoryCondition extends AbstractCondition<TblColumnHistory> {

	private String collectorId;
	private String startDate;

	private String endDate;

	private String collectorName;

	private String code;

	private String areaId;

	private String parentAreaIds;

	private String model;

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getCollectorName() {
		return collectorName;
	}

	public void setCollectorName(String collectorName) {
		this.collectorName = collectorName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getAreaId() {
		return areaId;
	}

	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}

	public String getParentAreaIds() {
		return parentAreaIds;
	}

	public void setParentAreaIds(String parentAreaIds) {
		this.parentAreaIds = parentAreaIds;
	}

	public String getCollectorId() {
		return collectorId;
	}

	public void setCollectorId(String collectorId) {
		this.collectorId = collectorId;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
}