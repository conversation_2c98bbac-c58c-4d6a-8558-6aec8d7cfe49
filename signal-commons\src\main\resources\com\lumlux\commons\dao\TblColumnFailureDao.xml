<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lumlux.commons.dao.TblColumnFailureDao">
	<sql id="tblFailureColumns">
		a.id AS "id",
		a.collector_id AS "collectorId",
		b.name AS "collectorName",
-- 		b.goal AS "collectorGoal",
		b.status AS "collectorStatus",
		a.status AS "status",
		b.failure_status AS "collectorFailureStatus",
		cc.label AS "failureStatus",
		a.description AS "description",
		a.end_date AS "endDate",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		d.name AS "area.name",
		e.name AS "office.name"
	</sql>
	
	<sql id="tblFailureJoins">
		left join tbl_column b ON a.`COLLECTOR_ID` = b.`ID`
		left join sys_dict cc on  a.status = cc.value and cc.type = 'failure_status'
		left join sys_area d ON d.id = b.area_id
		left join sys_office e ON e.id = b.office_id
	</sql>

	<sql id="whereForFindList">
		<if test="collectorId != null and collectorId != ''"> AND a.collector_id = #{collectorId}</if>
		<if test="office != null and office != ''">AND b.office_id =#{office.id}</if>
		<if test="name != null and name != ''"> AND b.name like concat('%',#{name},'%')</if>
		<if test="supportId != null and supportId != ''">AND b.support_id =#{supportId}</if>
		<if test="statusValue != null and statusValue != ''">
			<if test="statusValue == 0">
				AND (a.status =#{statusValue} or a.status = '1')
			</if>
			<if test="statusValue == 2">
				AND a.status =#{statusValue}
			</if>
		</if>
		<if test="statusList != null and statusList.size > 0">
			AND  a.status in
			<foreach collection="statusList" item="status" open="(" separator=","  close=")">
				#{status}
			</foreach>
		</if>
	</sql>

	<sql id="whereForFindList1">
		<if test="status != null and status != ''">AND a.status =#{status}</if>
		<if test="statusList != null and statusList.size > 0">
			AND  a.status in
			<foreach collection="statusList" item="status" open="(" separator=","  close=")">
				#{status}
			</foreach>
		</if>
	</sql>
    
	<select id="get" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT 
			<include refid="tblFailureColumns"/>
		FROM tbl_failure a
		<include refid="tblFailureJoins"/>
		WHERE a.id = #{id} AND b.`DEL_FLAG` = 0
	</select>
	
	<select id="findList" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT 
			<include refid="tblFailureColumns"/>
		FROM tbl_failure a
		<include refid="tblFailureJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL} AND b.`DEL_FLAG` = 0
			<include refid="whereForFindList1"/>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="findListByCondtion" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT 
			<include refid="tblFailureColumns"/>
		FROM tbl_failure a
			<include refid="tblFailureJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL} AND b.`DEL_FLAG` = 0
			<include refid="whereForFindList"/>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	
	<select id="findAllList" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT 
			<include refid="tblFailureColumns"/>
		FROM tbl_failure a
		<include refid="tblFailureJoins"/>
		<where>
			a.del_flag = #{DEL_FLAG_NORMAL} AND b.`DEL_FLAG` = 0
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO tbl_failure(
			id,
			collector_id,
			status,
			description,
			end_date,
			create_by,
			create_date,
			update_by,
			update_date,
			remarks,
			del_flag,
			goal
		) VALUES (
			#{id},
			#{collectorId},
			#{status},
			#{description},
			#{endDate},
			#{createBy.id},
			#{createDate},
			#{updateBy.id},
			#{updateDate},
			#{remarks},
			#{delFlag},
			#{goal}
		)
	</insert>
	
	<update id="update">
		UPDATE tbl_failure SET 	
			collector_id = #{collectorId},
			status = #{status},
			description = #{description},
			end_date = #{endDate},
			update_by = #{updateBy.id},
			update_date = #{updateDate},
			remarks = #{remarks}
		WHERE id = #{id}
	</update>
	
	<update id="delete">
		UPDATE tbl_failure SET 
			del_flag = #{DEL_FLAG_DELETE}
		WHERE id = #{id}
	</update>
	
	<update id="deleteFailure">
		UPDATE tbl_failure SET 
			del_flag = '1'
		WHERE collector_id = #{collectorId} and status != '2' and del_flag='0'
	</update>

	<update id="deleteBatch">
		UPDATE tbl_failure SET
		status = "2"
		WHERE id IN
		<foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<select id="updateStatus">
		update tbl_failure set status = #{0}
		where id = #{1}
	</select>
	
	<select id="getFailureHistory" resultType="com.lumlux.commons.entity.TblFailure">
		select c.`name` as name,
			c.address as address,
			b.description as description,
			d.light_address as circuitAddress,
			e.label label,
			a.status status,
			b.id as "failureDetail.id",
			b.report_type as "failureDetail.reportType",
			a.create_date as create_date,
			a.update_date as update_date
		from (tbl_failure a,tbl_failure_detail b)
		left join tbl_collector c on a.collector_id = c.id and c.del_flag = 0
		left join tbl_circuit d on b.circuit_id = d.id and d.del_flag = 0
		left join sys_dict e on d.light_position = e.value and e.type ='light_position' and e.del_flag=0
		where a.id = b.failure_id and a.del_flag = 0 and b.del_flag = 0
		<if test="collectorId !=null and collectorId != ''"> AND LOCATE(a.collector_Id,#{collectorId})>0 </if>
		<if test="circuitAddress !=null and circuitAddress != ''"> AND LOCATE(d.light_address,#{circuitAddress})>0 </if>
		<if test="statusValue !=null and statusValue != ''"> AND LOCATE(b.description,#{statusValue})>0 </if>
		<if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''"> AND a.create_date between #{startDate} and #{endDate} </if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.update_date DESC
			</otherwise>
		</choose>
	</select>

	<select id="getFailureHistorys" resultType="com.lumlux.commons.entity.TblFailure">
		select address as "area.name",dname as "collectorName",model as "collectorModel",description as "description",yes/(yes+nos)*100 as "evaluateRate"
		from (select df.address,df.dname,df.model,df.description,max(df.yes) yes,max(df.nos)nos,max(df.you)you,df.updateDate from (select address,dname,model,description, yes, nos,you,updateDate from (select address,dname,model,description,reportType, (case when reportType = '0' then count(*) ELSE 0 end) as 'yes', (case when reportType = '1' then count(*) ELSE 0 end) as 'nos' ,(case when reportType = '2' then count(*) ELSE 0 end) as 'you' , updateDate from (select area.`name` address,c.`name` as dname,c.`model`as model,b.description as description,b.report_type as reportType,date_format(b.update_date,'%Y-%m-%d %H:%i:%s') as updateDate from (tbl_failure a,tbl_failure_detail b)
		left join tbl_collector c on a.collector_id = c.id and c.del_flag = 0
		left join sys_area area on area.id = c.area_id and area.del_flag=0
		left join tbl_circuit d on b.circuit_id = d.id and d.del_flag = 0
		where a.id = b.failure_id and a.del_flag = 0 and b.del_flag = 0  ) cv
		GROUP BY address,dname,model,description,reportType)cc)df
		LEFT JOIN
		(select address,dname,model,description, yes, nos,you,updateDate from (select address,dname,model,description,reportType, (case when reportType = '0' then count(*) ELSE 0 end) as 'yes', (case when reportType = '1' then count(*) ELSE 0 end) as 'nos' ,(case when reportType = '2' then count(*) ELSE 0 end) as 'you' , updateDate from (select area.`name` address,c.`name` as dname,c.`model`as model,b.description as description,b.report_type as reportType,date_format(b.update_date,'%Y-%m-%d %H:%i:%s') as updateDate from (tbl_failure a,tbl_failure_detail b)
		left join tbl_collector c on a.collector_id = c.id and c.del_flag = 0
		left join sys_area area on area.id = c.area_id and area.del_flag=0
		left join tbl_circuit d on b.circuit_id = d.id and d.del_flag = 0
		where a.id = b.failure_id and a.del_flag = 0 and b.del_flag = 0  ) cv
		GROUP BY address,dname,model,description,reportType)cc) fd
		on fd.description=df.description
		GROUP BY df.dname,df.address,df.model,df.description)	gh
		WHERE yes+nos &gt; 0
		<if test="areaName !=null and areaName != ''">
		and	address like concat('%',#{areaName},'%')

		</if>
		<if test="collectorModel !=null and collectorModel != ''">
			AND model like concat('%',#{collectorModel},'%')
		</if>
		<if test="statusValue !=null and statusValue != ''">
			AND description = #{statusValue}
		</if>
		<if test="collectorName !=null and collectorName != ''">
			AND dname like concat('%',#{collectorName},'%')
		</if>
		<if test="startDate !=null and startDate != '' and endDate !=null and endDate != ''">
			AND updateDate between #{startDate} and #{endDate}
		</if>
	</select>




	<select id="findAllFailure" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT
		<include refid="tblFailureColumns"/>
		FROM tbl_failure a
		<include refid="tblFailureJoins"/>
		WHERE a.id = #{collectorId} AND b.`DEL_FLAG` = 0
	</select>
	<insert id="insertFailure">
		INSERT INTO
		tbl_circuit
		(
		id,
		collector_id,
		status,
		description,
		end_date,
		create_by,
		create_date,
		update_by,
		update_date,
		remarks,
		del_flag,
		goal
		)
		VALUES
		<foreach collection="list" item= "failure" index ="index" separator=",">
			(
			#{failure.id},
			#{failure.collectorId},
			#{failure.status},
			#{failure.description},
			#{failure.endDate},
			#{failure.createBy.id},
			#{failure.createDate},
			#{failure.updateBy.id},
			#{failure.updateDate},
			#{failure.remarks},
			#{failure.delFlag},
			1
			)
		</foreach >
	</insert>

<!--
	<select id="findAllFailureByGoal" resultType="com.lumlux.commons.entity.TblFailure">

		SELECT
		<include refid="tblFailureColumns"/>
		FROM tbl_failure a
		<include refid="tblFailureJoins"/>
		WHERE a.id = #{collectorId} AND b.`DEL_FLAG` = 0 and a.goal = 1
	</select>
-->

	<select id="byIdFailure" resultType="com.lumlux.commons.entity.TblFailure">
		SELECT * from  tbl_failure WHERE del_flag='0' and collector_id = #{collectorId}
	</select>

	<select id="listNumber" resultType="java.lang.Integer">
		select COUNT(*) number from tbl_repair_order WHERE collector_id=#{collectorId} and del_flag='0' and `status`='0'
	</select>
</mapper>