<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ attribute name="leftId" type="java.lang.String" required="false"%>
<%@ attribute name="leftNavUrl" type="java.lang.String" required="false"%>
<%@ attribute name="centreId" type="java.lang.String" required="false"%>

<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys"%>

<c:if test="${empty leftId}">
    <c:set var="leftId" value="variableNav" />
</c:if>
<c:if test="${empty centreId}">
    <c:set var="centreId" value="centent" />
</c:if>

<layout:default title="标志牌列表">
    <%@include file="/WEB-INF/views/include/treeview.jsp" %>
    <style type="text/css">
        .ztree {
            overflow: auto;
            margin: 0;
            _margin-top: 10px;
            padding: 10px 0 0 10px;
        }
    </style>
    <sys:message content="${message}"/>
    <div id="content" class="row-fluid">
        <div id="left" class="accordion-group">
            <div class="accordion-heading">
                <a class="accordion-toggle">标志牌位置列表</a>
            </div>
            <div id="ztree" class="ztree"></div>
        </div>
        <div id="openClose" class="close" style="width:15px ">&nbsp;</div>
        <div id="right">
            <iframe id="signBoardControllerFrame" name="signBoardControllerFrame" src="${ctx}/signal/tblCollector/listColumn?condition.areaId=&condition.parentAreaIds=&condition.model=14"
                    frameborder="no" width="100%" height="91%" ></iframe>
        </div>
    </div>



    <script type="text/javascript">
        var setting = {
            data: {simpleData: {enable: true, idKey: "id", pIdKey: "pId", rootPId: '1'}},
            callback: {
                onClick: function (event, treeId, treeNode) {
                    var id = treeNode.id == '0' ? '' : treeNode.id;
                    $('#signBoardControllerFrame').attr("src", "${ctx}/signal/tblCollector/listColumn?condition.areaId="+id+"&condition.parentAreaIds="+treeNode.pIds+"&condition.type=0&condition.model=14");
                }
            }
        };

        function refreshTree() {
            $.getJSON("${ctx}/signal/tblCollector/selectCollectorTreeData", function (data) {
                $.fn.zTree.init($("#ztree"), setting, data).expandAll(true);
            });
        }
        refreshTree();

        var leftWidth = 230; // 左侧窗口大小
        var htmlObj = $("html"), mainObj = $("#main");
        var frameObj = $("#left, #openClose, #right, #right iframe");
        function wSize() {
            var strs = getWindowSize().toString().split(",");
            htmlObj.css({"overflow-x": "hidden", "overflow-y": "hidden"});
            mainObj.css("width", "auto");
            frameObj.height(strs[0] - 5);
            var leftWidth = ($("#left").width() < 0 ? 0 : $("#left").width());
            $("#right").width($("#content").width() - leftWidth - $("#openClose").width() - 5);
            $(".ztree").width(leftWidth - 10).height(frameObj.height() - 46);
        }

    </script>
    <script src="${ctxStatic}/common/wsize.min.js" type="text/javascript"></script>
</layout:default>
