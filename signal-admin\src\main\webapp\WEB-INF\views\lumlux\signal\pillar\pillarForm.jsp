<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="layout" tagdir="/WEB-INF/tags/layout"%>
<%@ taglib prefix="shiro" uri="/WEB-INF/tlds/shiros.tld" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fns" uri="/WEB-INF/tlds/fns.tld" %>
<%@ taglib prefix="sys" tagdir="/WEB-INF/tags/sys" %>

<layout:default title="集中器表管理" bodyId="collectorId">
	<style>
		.input-width-20{
	    	width:20px;
	    	text-align: center;
	    }
	    .float-left{
	    	padding-left:2px;
	    	float:left;
	    }
	    .modal{
	    	left: 40%;
	    }
	</style>
	<script type="text/javascript">

		$(document).ready(function() {
			$("#inputForm").validate({
				submitHandler: function(form){
					loading('正在提交，请稍等...');
					form.submit();
				},
				errorContainer: "#messageBox",
				errorPlacement: function(error, element) {
					$("#messageBox").text("输入有误，请先更正。");
					if (element.is(":checkbox")||element.is(":radio")||element.parent().is(".input-append")){
						error.appendTo(element.parent().parent());
					} else {
						error.insertAfter(element);
					}
				}
			});

			$(".input-width-20").blur(function(){
				if(this.value.length==0){
					this.value="00";
				}else if(this.value.length==1){
					this.value= "0" + this.value;
				}else if(this.value.length==2){
					if($(this).hasClass("type-hour")){
						if(parseInt(this.value)<0){
							this.value="00";
						}else if(parseInt(this.value)>23){
							this.value="23";
						}
					}else if($(this).hasClass("type-minute")){
						if(parseInt(this.value)<0){
							this.value="00";
						}else if(parseInt(this.value)>59){
							this.value="59";
						}
					}
				}
			});
			initDataFormatter();

		});

		function collectorSubmit(){

			const paramKey = ["name","iccid","code","longitude", "latitude","address",
				"deviceInstallTime", "supplierId", "officeId", "supportId", "areaId", "prodDeliveryTime","prodEndTime", "remarks","qnlineThreshold","model"]
			const formItemName = [
				"dataEntity.name", "dataEntity.iccid","dataEntity.code","dataEntity.longitude","dataEntity.latitude",
				"dataEntity.address","dataEntity.deviceInstallTime","dataEntity.supplierId","dataEntity.officeId","dataEntity.supportId",
				"dataEntity.areaId","dataEntity.prodDeliveryTime","dataEntity.prodEndTime","dataEntity.remarks","dataEntity.qnlineThreshold","dataEntity.model"]

			const rawParams = $("#inputForm").serializeArray()
			let params = {}

			for (let i = 0; i < paramKey.length; i++)
				params[paramKey[i]] = rawParams.filter(e=> e.name == formItemName[i])[0]['value']
			params = {...params}
			top.$.jBox.tip("正在提交，请稍等...",'loading',{opacity:0});
			if('${form.dataEntity.id}') {
				params.id = '${form.dataEntity.id}'
				$.ajax({
					type: "post",
					url: "${path}/column/a/signal/tblColumn/updateColumn",
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					data: JSON.stringify(params),
					success: result => {
						if(result.code == 200)
							top.$.jBox.tip("保存成功", 'success', {opacity: 0});
						else
							top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
					},
					error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0})
				})
			} else {
				$.ajax({
					type: "post",
					url: "${path}/column/a/signal/tblColumn/addColumn",
					contentType: "application/json; charset=utf-8",
					dataType: "json",
					data: JSON.stringify(params),
					success: result => {
						if(result.code == 200)
							top.$.jBox.tip("保存成功", 'success', {opacity: 0});
						else
							top.$.jBox.tip("保存失败" + (result.msg == "错误" ? "" : ("," + result.msg)), 'error', {opacity: 0});
					},
					error: _ => top.$.jBox.tip("保存失败", 'error', {opacity: 0})
				})
			}


		}

		function selectLocation(){
			$.jBox("iframe:${pageContext.request.contextPath}/selectCollectorLocation.jsp",{
						title: "选择经纬度",
						width: 600,
						height: 400,
						buttons: { '关闭': 1 ,'确定':2},
						submit: function (v, h, f) {


					},
					loaded:function(h){

			}
		});
		}

		function  selectLocationCallBack(longitude,latitude){
			$("#longitude").val(longitude);
			$("#latitude").val(latitude);
		}

		$(function(){
			$("#citySelect").change(function(data){
				$.ajax({
					url:"/lumlux/a/signal/tblCollector/selectAreasByParentId?parentId="+data.val,
					type:"get",
					success:function(data){
						var html = "";
						for(var i =0;i<data.length;i++){
							html += "<option value='"+data[i].id+"'>"+data[i].name+"</option>";
						}
						$("#regionSelect").append(html);

					},
					error: function (e) {
						console.log(e);
					}
				})
			});

			$("#regionSelect").change(function(data){
				$.ajax({
					url:"/lumlux/a/signal/tblCollector/selectAreasByParentId?parentId="+data.val,
					type:"get",
					success:function(data){
						var html = "";
						for(var i =0;i<data.length;i++){
							html += "<option value='"+data[i].id+"'>"+data[i].name+"</option>";
						}
						$("#brigadeSelect").append(html);
					},
					error: function (e) {
						console.log(e);
					}
				})
			});

			$("#brigadeSelect").change(function(data){
				$.ajax({
					url:"/lumlux/a/signal/tblCollector/selectAreasByParentId?parentId="+data.val,
					type:"get",
					success:function(data){
						var html = "";
						for(var i =0;i<data.length;i++){
							html += "<option value='"+data[i].id+"'>"+data[i].name+"</option>";
						}
						$("#roadSelect").append(html);
					},
					error: function (e) {
						console.log(e);
					}
				})
			});
		});


		// 将Date转化为"yyyy-MM-dd"或"yyyy-MM"格式
		function dateFormat(date, requireDays) {
			return requireDays ? date.getFullYear() + "-" +
					(date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth()) + "-" +
					(date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) :
					date.getFullYear() + "-" + (date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth());
		}

		function initDataFormatter() {
			jQuery.validator.addMethod('dateVaildate', value => new RegExp(
					"^(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-" +
					"(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-" +
					"(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|1[0-9]|2[0-8]))))|" +
					"((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
					"((0[48]|[2468][048]|[13579][26])00))-02-29)$").test(value) || value == '')

			jQuery.validator.addMethod('timeRangeValidate', value => new RegExp(
					/^(20|21|22|23|[0-1]\d):[0-5]\d-(20|21|22|23|[0-1]\d):[0-5]\d$/).test(value))

			jQuery.extend(jQuery.validator.messages, { dateValidate: "格式要求：年-月-日（例如：2023-06-01）",
				timeRangeValidate:"输入不符合格式要求（例：08:00-10:20）" });

			// 修改安装时间格式

			let prodDeliveryTime = "${form.dataEntity.prodDeliveryTime}";
			let prodEndTime = "${form.dataEntity.prodEndTime}";
			let deviceInstallTime = "${form.dataEntity.deviceInstallTime}";
			document.getElementById("dataEntity.deviceInstallTime").value = deviceInstallTime ? dateFormat(new Date(deviceInstallTime), true) : '';
			document.getElementById("dataEntity.prodDeliveryTime").value = prodDeliveryTime ? dateFormat(new Date(prodDeliveryTime), true) : '';
			document.getElementById("dataEntity.prodEndTime").value = prodEndTime ? dateFormat(new Date(prodEndTime), true) : '';
		}





	</script>

	<ul class="nav nav-tabs">
<%--		<li class="active"><a href="${ctx}/signal/tblCollector/form?id=${tblCollector.id}">集中器表<shiro:hasPermission name="signal:tblCollector:edit">${not empty tblCollector.id?'修改':'添加'}</shiro:hasPermission><shiro:lacksPermission name="signal:tblCollector:edit">查看</shiro:lacksPermission></a></li>--%>
<%--			<li><a href="${ctx}/signal/tblColumn/listColumn?condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">控制器列表</a></li>--%>
			<li><a href="${ctx}/signal/tblCollector/listColumn?condition.model=13&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}">立柱列表</a></li>
		  	<li class="active">
			  	<a>
				<shiro:hasPermission name="signal:tblColumn:edit">${not empty form.dataEntity.id?'修改立柱':'添加立柱'}</shiro:hasPermission>
			  	<shiro:lacksPermission name="signal:tblColumn:edit">查看立柱</shiro:lacksPermission></a></li>
		  		</a>
			</li>

	</ul><br/>
	<form:form id="inputForm" modelAttribute="form" action="${ctx}/signal/tblColumn/addColumn" method="post" class="form-horizontal">
		<form:hidden path="dataEntity.id"/>
		<form:hidden path="dataEntity.model" value="13"/>
		<form:hidden path="condition.page.pageNo"/>
		<form:hidden path="condition.page.pageSize"/>
		<form:hidden path="condition.name"/>
		<sys:message content="${message}"/>
		<sys:message content="${ifExistMsg}" type="error"/>
<%--		<form:hidden path="condition.page.pageSize"/>
		<!-- todo others condition -->
		<form:hidden path="condition.name"/>
		<form:hidden path="dataEntity.isEdit"/>

		<sys:message content="${message}"/>
		<sys:message content="${ifExistMsg}" type="error"/>--%>

		<div class="control-group">
			<label class="control-label">名称：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:input path="dataEntity.name" htmlEscape="false" maxlength="20" class="input-xlarge required"/>
                            <span class="help-inline"><font color="red">*</font> </span>
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:view">
                            <span>${form.dataEntity.name}</span>
                        </shiro:lacksPermission>
                    </div>
		</div>

		<div class="control-group">
			<label class="control-label">编码：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblColumn:edit">
					<form:input path="dataEntity.code" htmlEscape="false" maxlength="11" minlength="11" class="input-xlarge required number"/>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblColumn:view">
					<span>${form.dataEntity.code}</span>
				</shiro:lacksPermission>
			</div>
		</div>

		<%--<div class="control-group" style="display: none" >
			<label class="control-label">型号：</label>

			<div class="controls">
				<shiro:hasPermission name="signal:tblColumn:edit">
					<form:select id="collectorModel" path="dataEntity.model"  cssStyle="width: 290px"  class="input-xlarge required" >
						<form:option value="">请选择型号</form:option>
						<c:forEach items="${columnType}" var="sysDictionaries">
								<form:option value="${sysDictionaries.value}">${sysDictionaries.description}</form:option>
						</c:forEach>
					</form:select>
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblColumn:edit">
					<span>${form.dataEntity.model}</span>
				</shiro:lacksPermission>
			</div>
		</div>--%>



		<div class="control-group">
			<label class="control-label">物联网卡号(ICCID)：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:input path="dataEntity.iccid" htmlEscape="false" maxlength="20" minlength="20" class="input-xlarge required iccidVaildate"/>
                            <span class="help-inline"><font color="red">*</font> </span>
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:view">
                            <span>${form.dataEntity.iccid}</span>
                        </shiro:lacksPermission>
                    </div>
		</div>

		<div class="control-group">
			<label class="control-label">经纬度：</label>
				<div class="controls">
                    <shiro:hasPermission name="signal:tblColumn:edit">
                        <form:input id="longitude" path="dataEntity.longitude" htmlEscape="false" maxlength="16" class="input-small required number"/>
                        <form:input id="latitude" path="dataEntity.latitude" htmlEscape="false" maxlength="16" class="input-small required number"/>
                        <span class="help-inline"><font color="red">*</font> </span>
                        <input class="btn btn-primary" type="button" value="选 择" onclick="selectLocation()"/>
                        <div id="tip"></div>
                    </shiro:hasPermission>
                    <shiro:lacksPermission name="signal:tblColumn:edit">
                        <span>经度${form.dataEntity.longitude}</span>&nbsp;<span>纬度${form.dataEntity.latitude}</span>
                    </shiro:lacksPermission>
                </div>
		</div>

		<div class="control-group">
			<label class="control-label">地址：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:input path="dataEntity.address" htmlEscape="false" class="input-xlarge required"  maxlength="20" />
                            <span class="help-inline"><font color="red">*</font> </span>
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:view">
                            <span>${form.dataEntity.address}</span>
                        </shiro:lacksPermission>
                    </div>
		</div>

		<div class="control-group">
			<label class="control-label">设备安装时间：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:input path="dataEntity.deviceInstallTime" htmlEscape="false" class="input-xlarge dateVaildate" maxlength="10"/>
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:edit">
                            <span><fmt:formatDate value="${form.dataEntity.deviceInstallTime}" pattern="yyyy-MM-dd"/></span>
                        </shiro:lacksPermission>
                    </div>
		</div>

		<div class="control-group">
			<label class="control-label">制造商：</label>
				<div class="controls">
                    <shiro:hasPermission name="signal:tblColumn:edit">
                        <form:select path="dataEntity.supplierId"  cssStyle="width: 290px"  class="input-xlarge required" >
                            <form:option value="">请选择制造商</form:option>
                            <form:options items="${suppliers}" itemValue="id" itemLabel="name"  ></form:options>
                        </form:select>
                        <span class="help-inline"><font color="red">*</font> </span>
                    </shiro:hasPermission>
                    <shiro:lacksPermission name="signal:tblColumn:edit">
                        <span>${form.dataEntity.supplierName}</span>
                    </shiro:lacksPermission>
                </div>
		</div>

		<div class="control-group">
			<label class="control-label">管理单位：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:select path="dataEntity.officeId"    class="input-xlarge required" cssStyle="width: 290px" >
                                <form:option value="">请选择管理单位</form:option>
                                <form:options items="${offices}" itemValue="id" itemLabel="name"></form:options>
                            </form:select>
                            <span class="help-inline"><font color="red">*</font> </span>
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:edit">
                            <span>${form.dataEntity.office.name}</span>
                        </shiro:lacksPermission>
                    </div>
		</div>

		<div class="control-group">
			<label class="control-label">维护单位：</label>
				<div class="controls">
                    <shiro:hasPermission name="signal:tblColumn:edit">
                        <form:select path="dataEntity.supportId"   cssStyle="width: 290px"  class="input-xlarge required">
                            <form:option value="">请选择维护单位</form:option>
                            <form:options items="${supports}" itemValue="id" itemLabel="name"></form:options>
                        </form:select>
                        <span class="help-inline"><font color="red">*</font> </span>
                    </shiro:hasPermission>
                    <shiro:lacksPermission name="signal:tblColumn:edit">
                        <span>${form.dataEntity.supportName}</span>
                    </shiro:lacksPermission>
                </div>
		</div>
		<div class="control-group">
			<label class="control-label">所属区域：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblColumn:edit">
					<sys:treeselect id="area" name="dataEntity.areaId" value="${form.dataEntity.areaId}" labelName="area.name" labelValue="${form.dataEntity.area.name}"
									title="区域" url="/signal/tblCollector/treeData" />
					<span class="help-inline"><font color="red">*</font> </span>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblColumn:edit">
					<span>${form.dataEntity.area.name}</span>
				</shiro:lacksPermission>
			</div>
		</div>


		<div class="control-group">
			<label class="control-label">产品交付时间：</label>
				<div class="controls">
                    <shiro:hasPermission name="signal:tblColumn:edit">
                        <form:input path="dataEntity.prodDeliveryTime" htmlEscape="false" class="input-xlarge dateVaildate" maxlength="10"/>
                    </shiro:hasPermission>
                    <shiro:lacksPermission name="signal:tblColumn:edit">
                        <span><fmt:formatDate value="${form.dataEntity.prodDeliveryTime}" pattern="yyyy-MM-dd"/></span>
                    </shiro:lacksPermission>
                </div>
		</div>

		<div class="control-group">
			<label class="control-label">产品质保时间：</label>
				<div class="controls">
                    <shiro:hasPermission name="signal:tblColumn:edit">
                        <form:input path="dataEntity.prodEndTime" htmlEscape="false" class="input-xlarge dateVaildate required" maxlength="10"/>
                        <span class="help-inline"><font color="red">*</font> </span>
                    </shiro:hasPermission>
                    <shiro:lacksPermission name="signal:tblColumn:edit">
                        <span><fmt:formatDate value="${form.dataEntity.prodEndTime}" pattern="yyyy-MM-dd"/></span>
                    </shiro:lacksPermission>
                </div>
		</div>

		<div class="control-group">
			<label class="control-label">立柱在线阈值：</label>
			<div class="controls">
				<shiro:hasPermission name="signal:tblColumn:edit">
					<form:input path="dataEntity.qnlineThreshold" htmlEscape="false" class="input-xlarge" maxlength="10"/>
				</shiro:hasPermission>
				<shiro:lacksPermission name="signal:tblColumn:edit">
					<span><fmt:formatDate value="${form.dataEntity.qnlineThreshold}" pattern="yyyy-MM-dd"/></span>
				</shiro:lacksPermission>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label">备注：</label>
					<div class="controls">
                        <shiro:hasPermission name="signal:tblColumn:edit">
                            <form:textarea path="dataEntity.remarks" rows="4" cols="20"  maxlength="256" style="resize:none;width:270px;" />
                        </shiro:hasPermission>
                        <shiro:lacksPermission name="signal:tblColumn:edit">
                            <span>${form.dataEntity.remarks}</span>
                        </shiro:lacksPermission>
                    </div>
		</div>
		<div class="form-actions" style="padding-left:130px;">
				<%--			<shiro:hasPermission name="signal:pillar:add">--%>
			<shiro:hasPermission name="signal:tblColumn:add">
				<input id="btnSubmit" onclick="collectorSubmit()" class="btn btn-primary" type="button" value="保 存"/>&nbsp;
				<c:if test='${empty form.dataEntity.id}'>
					<a id="reset" class="btn btn-primary" href="">重置</a>&nbsp;
				</c:if>
			</shiro:hasPermission>
			<shiro:hasPermission name="signal:tblColumn:view">
				<c:if test='${not empty form.dataEntity.id}'>
<%--					<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/secondDetail?condition.collectorId=${form.dataEntity.id}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" >参数配置</a>&nbsp;--%>
					<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/pillarSecondParamDetail?condition.collectorId=${form.dataEntity.id}" >参数配置</a>&nbsp;
				</c:if>
			</shiro:hasPermission>
			<%--	<shiro:hasPermission name="signal:tblColumn:view">
					<c:if test='${not empty form.dataEntity.id and form.dataEntity.redProjectorEnabled ==1 }'>
						&lt;%&ndash;				<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/secondDetailLie?condition.collectorId=${form.dataEntity.id}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" >详细配置</a>&nbsp;&ndash;%&gt;
						<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/touYingDetails?condition.collectorId=${form.dataEntity.id}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" >红投影配置</a>&nbsp;
					</c:if>
				</shiro:hasPermission>--%>
					<shiro:hasPermission name="signal:tblColumn:view">
						<c:if test='${not empty form.dataEntity.id}'>
							<%--				<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/secondDetailLie?condition.collectorId=${form.dataEntity.id}&condition.areaId=${form.condition.areaId}&condition.parentAreaIds=${form.condition.parentAreaIds}" >详细配置</a>&nbsp;--%>
							<a class="btn btn-primary" href="${ctx}/signal/tblAlarmConfig/pillarDetail?condition.collectorId=${form.dataEntity.id}&condition.model=13" >详细配置</a>&nbsp;
						</c:if>
					</shiro:hasPermission>

			<a id="btnCancel" class="btn" onclick="javascript:window.history.back();return false;">返回</a>
		</div>

	</form:form>
</layout:default>