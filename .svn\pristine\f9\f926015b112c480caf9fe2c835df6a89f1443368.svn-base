package com.lumlux.signal.daemon.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import com.lumlux.signal.daemon.bean.TblCollector;
import com.lumlux.signal.daemon.util.RowMapper;

public final class TblCollectorRowMapper implements RowMapper<TblCollector> {

	@Override
	public TblCollector mapRow(ResultSet rs, int index)
			throws SQLException {
		TblCollector to = new TblCollector();
		to.setId(rs.getString("id"));
		to.setName(rs.getString("name"));
		to.setCode(rs.getString("code"));
		to.setAreaId(rs.getString("area_id"));
		to.setBoardQuantity(rs.getByte("board_quantity"));
		to.setLongitude(rs.getString("longitude"));
		to.setLatitude(rs.getString("latitude"));
		to.setGeoHashCode(rs.getString("geo_hash_code"));
		to.setPictureUrl(rs.getString("picture_url"));
		to.setEnabled(rs.getString("enabled"));
		to.setStatus(rs.getString("status"));
		to.setFailureStatus(rs.getString("failure_status"));
		to.setSupplierId(rs.getString("supplier_id"));
		to.setSupportId(rs.getString("support_id"));
		to.setOfficeId(rs.getString("office_id"));
		to.setSimNo(rs.getString("sim_no"));
		to.setInstallPosition(rs.getString("install_position"));
		to.setCreateBy(rs.getString("create_by"));
		to.setCreateDate(rs.getTimestamp("create_date"));
		to.setUpdateBy(rs.getString("update_by"));
		to.setUpdateDate(rs.getTimestamp("update_date"));
		to.setAddress(rs.getString("address"));
		to.setRemarks(rs.getString("remarks"));
		to.setDelFlag(rs.getString("del_flag").charAt(0));
		to.setModel(rs.getString("model"));
		to.setPowerMode(rs.getString("power_mode"));
		to.setRelayStatus(rs.getString("relay_status"));
		to.setFailureDate(rs.getTime("failure_date"));
		to.setVersion(rs.getString("version"));
		to.setControlMode(rs.getString("control_mode"));
		to.setIsInform(rs.getString("is_Inform"));
		to.setReadTime(rs.getString("read_time"));
		to.setLedCount(rs.getString("led_count"));
		to.setLineCount(rs.getString("line_count"));
		to.setReduceTime(rs.getString("reduce_time"));
		to.setIsReduce(rs.getString("is_reduce"));
		to.setOnlineMinutes(rs.getInt("onlineMinutes"));
		to.setCounter(rs.getInt("counter"));
		to.setPutFlag(rs.getString("put_flag"));
		to.setTimeSpanA(rs.getString("time_span_a"));
		to.setTimeSpanB(rs.getString("time_span_b"));
		to.setMagneticLockGoal(rs.getInt("magneticLockGoal"));
//		to.setActivationTime(rs.getDate("activationTime"));
		return to;
	}
}
