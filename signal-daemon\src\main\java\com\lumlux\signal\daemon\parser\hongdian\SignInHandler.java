package com.lumlux.signal.daemon.parser.hongdian;

import com.lumlux.signal.daemon.bean.SysDictionary;
import com.lumlux.signal.daemon.bean.SysUserApp;
import com.lumlux.signal.daemon.bean.TblCollector;
import com.lumlux.signal.daemon.bean.TblFailure;
import com.lumlux.signal.daemon.rowmapper.SysDictionaryRowMapper;
import com.lumlux.signal.daemon.rowmapper.SysUserAppRowMapper;
import com.lumlux.signal.daemon.rowmapper.TblCollectorRowMapper;
import com.lumlux.signal.daemon.rowmapper.TblFailureRowMapper;
import com.lumlux.signal.daemon.util.DataSourceUtil;
import com.lumlux.signal.daemon.util.ExecuteDbOperate;
import com.lumlux.signal.daemon.util.PreparedParamCallBack;

import org.apache.log4j.Logger;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.session.IoSession;

import com.lumlux.signal.daemon.mina.SignalSessionIdManager;
import com.lumlux.signal.daemon.parser.AbstractHandler;

import java.sql.*;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 宏电协议终端注册处理器
 */
public class SignInHandler extends AbstractHandler
{

	private transient static Logger log = Logger.getLogger(SignInHandler.class);

	private static final String UPDATE_USER_ID = "1";
	private static final String SQL_UPDATE_COLLECTOR = "UPDATE `tbl_collector` SET `status`= ? ,`failure_status`= ? ,`failure_date`= ? , `update_by` = ?,`update_date` = ? WHERE `id` = ?";
	private static final String SQL_QUERY_COLLECTOR = "select * from tbl_collector where code= ? and del_flag= '0'";
	/*private static final String SQL_QUERY_FAILURE = "select * from tbl_failure where collector_id = ? and status != '2' and del_flag = '0'";*/
	private static final String SQL_UPDATE_FAILURE = "UPDATE `tbl_failure` SET `status`= '2',`update_by` = ?,`update_date` = ? WHERE `collector_id` = ? and del_flag = '0' and description = '离线' and `status` != '2'";
	private static final String SQL_SELECT_FAILURE = "select * from `tbl_failure` where `collector_id` = ? and del_flag = '0' and status != '2' and description = '离线'";
	private static final String SQL_SELECT_DICTIONARY = "select * from sys_dict where type='huiway_phone' and del_flag = '0'";
	private static final String SQL_INSERT_INFORM_DETAIL = "INSERT INTO `tbl_inform_detail` (`id`, `collector_id`, `collector_address`, `failure_id`, `type`, `object`, `content`, `is_inform`, `count`, `reply`, `last_time`, `create_by`, `create_date`, `update_by`, `update_date`, `remarks`, `del_flag`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String SQL_SELECT_SYS_USER_APP = "select * from sys_user_app where login_name = (select login_name from sys_user where mobile = ? and del_flag='0' limit 1)";
	
	private LinkedBlockingQueue<String> linkedBlockingQueue;
	
	public SignInHandler(LinkedBlockingQueue<String> linkedBlockingQueue)
	{
		this.linkedBlockingQueue = linkedBlockingQueue;
	}
	
	@Override
	public int handle(byte[] data, final IoSession session)
	{
		String threadName = Thread.currentThread().getName();

		// 终端注册（DTU-->DSC）
		final byte[] baImei = new byte[11];
		System.arraycopy(data, 4, baImei, 0, 11);
		final String strBaImei = new String(baImei);
		
		// 注册成功应答（DSC->DTU）
		HongdianUtil.SignInResponse(session, baImei, strBaImei);	
		
		log.info(threadName + " [SignInHandler] 注册心跳:" + strBaImei);
		
		// 心跳正常，恢复数据
		normalCollector(strBaImei);

		return 0;
	}

	// 正常
	private void normalCollector(final String strBaImei)
	{
		final String threadName = Thread.currentThread().getName();
		DataSourceUtil.execute(new ExecuteDbOperate()
		{
			@Override
			public int execute(Connection connection) throws SQLException
			{
				log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_QUERY_COLLECTOR);
				final TblCollector collector = DataSourceUtil.fillObject(SQL_QUERY_COLLECTOR, new TblCollectorRowMapper(), new PreparedParamCallBack()
				{
					@Override
					public int setParams(PreparedStatement ps) throws SQLException
					{
						ps.setString(1, strBaImei);
						return 0;
					}
				}, connection);
				log.debug(threadName + " [SignInHandler] Parameter   ===> " + strBaImei);
				if (collector != null)
				{
					// 更新集中器状态
					boolean hasCollectorFailure = false;
					List<String> collectFailureIdList = new ArrayList<String>();
					Timestamp timestamp = new Timestamp(System.currentTimeMillis());
					// 判断该集中器是否有未处理完成的回路故障
					log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_SELECT_FAILURE);
					List<TblFailure> failures = DataSourceUtil.fillList(SQL_SELECT_FAILURE, new TblFailureRowMapper(), new PreparedParamCallBack()
					{
						@Override
						public int setParams(PreparedStatement ps) throws SQLException
						{
							ps.setString(1, collector.getId());
							return 0;
						}
					}, connection);
					log.debug(threadName + " [SignInHandler] Parameter   ===> " + collector.getId());
					/*if (failures.size() > 0)
					{
						for (TblFailure tblFailure : failures)
						{
							if ("离线".equals(tblFailure.getDescription()))
							{
								hasCollectorFailure = true;
								collectFailureIdList.add(tblFailure.getId());
							}
							else
							{
								failureStatus = "2";
							}
						}
					}*/



					log.debug(threadName + " [SignInHandler] 恢复正常心跳，更新集中器状态");
					// 如果有未处理完成的集中器故障
					String failureStatus = "2".equals(collector.getFailureStatus())?"2":"0";
					log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_UPDATE_COLLECTOR);
					PreparedStatement ps = connection.prepareStatement(SQL_UPDATE_COLLECTOR);
					ps.setString(1, "0");
					ps.setString(2, failureStatus);
					ps.setTimestamp(3, timestamp);
					/*if (collector.getActivationTime()==null) {
						ps.setTimestamp(4, timestamp);
					}else {
						ps.setTimestamp(4,Timestamp.valueOf(new Date(collector.getActivationTime().getTime()).toString()));
					}*/
					ps.setString(4, UPDATE_USER_ID);
					ps.setTimestamp(5, timestamp);
					ps.setString(6, collector.getId());
					ps.execute();
					ps.close();
					log.debug(threadName + " [SignInHandler] Parameter   ===> " + "0" + ","  + failureStatus + ","  + timestamp + ","  + UPDATE_USER_ID + ","  + timestamp + ","  + collector.getId());
					
					if (failures.size() > 0)
					{
						for (int i = 0; i < failures.size(); i++)
						{
							log.debug(threadName + " [SignInHandler] 恢复正常心跳，删除集中器故障单" + failures.get(i).getId());
							log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_UPDATE_FAILURE);
							PreparedStatement pst = connection.prepareStatement(SQL_UPDATE_FAILURE);
							pst.setString(1, UPDATE_USER_ID);
							pst.setTimestamp(2, timestamp);
							pst.setString(3, collector.getId());
							pst.execute();
							pst.close();
							log.debug(threadName + " [SignInHandler] Parameter   ===> " + UPDATE_USER_ID+ ","+ timestamp + "," + collector.getId());
						}
						
						//离线恢复通知
						if("1".equals(collector.getIsInform()))
						{
							log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_SELECT_DICTIONARY);
							List<SysDictionary> dicList = DataSourceUtil.fillList(SQL_SELECT_DICTIONARY, new SysDictionaryRowMapper(), new PreparedParamCallBack()
							{
								@Override
								public int setParams(PreparedStatement ps) throws SQLException
								{
									return 0;
								}
							}, connection);
							if(dicList!=null && dicList.size()>0)
							{
								for (final SysDictionary dictionary : dicList) 
								{
									TblFailure tblFailure = failures.get(0);
									for (int i = 5; i < 8; i++)
									{
										//离线恢复不打电话
										if(i==6)
										{
											continue;
										}
										String type = "";
										String object = dictionary.getLabel();
										if(i==5)
										{
											type = i + "";
											log.debug(threadName + " [SignInHandler] Execute SQL ===> " + SQL_SELECT_SYS_USER_APP);
											SysUserApp sysUserApp = DataSourceUtil.fillObject(SQL_SELECT_SYS_USER_APP, new SysUserAppRowMapper(), new PreparedParamCallBack()
											{
												@Override
												public int setParams(PreparedStatement ps) throws SQLException
												{
													ps.setString(1, dictionary.getLabel());
													return 0;
												}
											}, connection);
											log.debug(threadName + " [SignInHandler] Parameter   ===> " + dictionary.getLabel());
											if(sysUserApp!=null && sysUserApp.getPublicOpenId()!=null && !sysUserApp.getPublicOpenId().isEmpty())
											{
												object = sysUserApp.getPublicOpenId();
											}
											else
											{
												continue;
											}
										}
										else
										{
											type = i + "";
										}
									
										String informId = DataSourceUtil.uuid();
										log.debug(threadName + " [SignInHandler] 创建通知详情 Execute SQL ===> " + SQL_INSERT_INFORM_DETAIL);
										ps = connection.prepareStatement(SQL_INSERT_INFORM_DETAIL);
										ps.setString(1,informId);
										ps.setString(2,collector.getId());
										ps.setString(3,collector.getAddress());
										ps.setString(4,tblFailure.getId());
										ps.setString(5,type);
										ps.setString(6,object);
										ps.setString(7,"离线");
										ps.setString(8,"0");
										ps.setString(9,"0");
										ps.setString(10,"");
										ps.setTimestamp(11,null);
										ps.setString(12,UPDATE_USER_ID);
							 			ps.setTimestamp(13,timestamp);
										ps.setString(14,UPDATE_USER_ID);
										ps.setTimestamp(15,timestamp);
										ps.setString(16,"");
										ps.setString(17,"0");
										ps.execute();
										ps.close();
										log.debug(threadName + " [SignInHandler] Parameter   ===> "+ informId + "," + collector.getId() + ","+ collector.getAddress() + "," + tblFailure.getId() + ","  + "7" + ","  + dictionary.getLabel() + "," + "离线" + "," + "0" + "," + "0" + "," + "" + "," + "null" + "," + UPDATE_USER_ID + ","  + timestamp + ","  + UPDATE_USER_ID + ","  + timestamp + ","  + "" + ","  + "0");
										try
										{
											linkedBlockingQueue.put(informId);
										}
										catch (InterruptedException e)
										{
											log.debug(e.getMessage());
										}
									}
									
								}
							}
						}
					}
					
				}
				else
				{
					log.debug(threadName + " [SignInHandler] 不存在此集中器");
				}
				return 0;
			}
		});
	}
}
